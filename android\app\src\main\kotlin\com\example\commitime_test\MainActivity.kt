package com.example.commitime_test

import android.annotation.SuppressLint
import android.annotation.TargetApi
import android.app.AppOpsManager
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.util.Log
import androidx.annotation.NonNull
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import io.flutter.plugins.GeneratedPluginRegistrant
import android.app.usage.UsageStatsManager
import java.util.*

class MainActivity: FlutterActivity(), MethodCallHandler {
    private val CHANNEL = "com.example.app_usage_tracker/channel"
    private val PREFS_NAME = "AppUsageTrackerPrefs"
    private val PREF_PREFIX_LIMIT = "limit_"
    private val USAGE_STATS_PERMISSION_REQUEST_CODE = 101
    private val OVERLAY_PERMISSION_REQUEST_CODE = 102

    private lateinit var sharedPreferences: SharedPreferences

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        sharedPreferences = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler(this)

        // Check if we need to navigate to a specific route
        intent?.extras?.getString("route")?.let { route ->
            // Check if we have arguments to pass to the route
            val appName = intent?.extras?.getString("app_name")
            val usageTime = intent?.extras?.getString("usage_time")

            if (appName != null && usageTime != null) {
                // Create a map of arguments to pass to the route
                val argsMap = mapOf(
                    "app_name" to appName,
                    "usage_time" to usageTime
                )

                // We can't directly pass arguments to pushRoute, so we'll use a workaround
                // Store the arguments in shared preferences
                val prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                val result = prefs.edit()
                    .putString("chat_app_name", appName)
                    .putString("chat_usage_time", usageTime)
                    .commit()  // Use commit() instead of apply() for immediate write

                // Log for debugging
                Log.d("MainActivity", "Stored in SharedPrefs: chat_app_name=$appName, chat_usage_time=$usageTime, result=$result")

                // Also call a Flutter method to ensure the data is properly stored
                try {
                    val methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "com.example.commitime_test/methods")
                    val arguments = HashMap<String, String>()
                    arguments["app_name"] = appName
                    arguments["usage_time"] = usageTime

                    methodChannel.invokeMethod("saveChatParameters", arguments, object : MethodChannel.Result {
                        override fun success(result: Any?) {
                            Log.d("MainActivity", "Successfully called saveChatParameters: $result")
                        }

                        override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                            Log.e("MainActivity", "Error calling saveChatParameters: $errorCode, $errorMessage")
                        }

                        override fun notImplemented() {
                            Log.e("MainActivity", "saveChatParameters not implemented")
                        }
                    })
                } catch (e: Exception) {
                    Log.e("MainActivity", "Exception calling saveChatParameters: ${e.message}")
                }

                // Bring app to foreground first
                val bringToFrontIntent = Intent(this, MainActivity::class.java)
                bringToFrontIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or
                                          Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
                startActivity(bringToFrontIntent)

                // Small delay to ensure app is in foreground
                Thread.sleep(100)

                // Then push route
                flutterEngine.navigationChannel.pushRoute(route)
                Log.d("MainActivity", "Pushing route $route with args stored in prefs: $argsMap")
            } else {
                // Bring app to foreground first
                val bringToFrontIntent = Intent(this, MainActivity::class.java)
                bringToFrontIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or
                                          Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
                startActivity(bringToFrontIntent)

                // Small delay to ensure app is in foreground
                Thread.sleep(100)

                // Then push route without arguments
                flutterEngine.navigationChannel.pushRoute(route)
                Log.d("MainActivity", "Pushing route $route without args")
            }
        }
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "checkUsageStatsPermission" -> {
                result.success(hasUsageStatsPermission())
            }
            "requestUsageStatsPermission" -> {
                requestUsageStatsPermission()
                result.success(null)
            }
            "checkOverlayPermission" -> {
                result.success(hasOverlayPermission())
            }
            "requestOverlayPermission" -> {
                requestOverlayPermission()
                result.success(null)
            }
            "startMonitoringService" -> {
                val started = startMonitoringService()
                result.success(started)
            }
            "getUsageStats" -> {
                val days = call.argument<Int>("days") ?: 1
                result.success(getUsageStats(days))
            }
            "getInstalledApps" -> {
                result.success(getInstalledApps())
            }
            "setAppLimit" -> {
                val packageName = call.argument<String>("packageName")
                val limitMinutes = call.argument<Int>("limitMinutes")

                if (packageName != null && limitMinutes != null) {
                    setAppLimit(packageName, limitMinutes)
                    result.success(true)
                } else {
                    result.error("INVALID_ARGUMENTS", "Package name and limit minutes required", null)
                }
            }
            "getAppLimits" -> {
                result.success(getAppLimits())
            }
            "reloadAppLimits" -> {
                // Tell the service to reload limits from shared preferences
                val intent = Intent(this, AppUsageMonitorService::class.java)
                intent.action = AppUsageMonitorService.ACTION_RELOAD_LIMITS
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    startForegroundService(intent)
                } else {
                    startService(intent)
                }
                result.success(true)
            }
            "stopMonitoringService" -> {
                val stopped = stopMonitoringService()
                result.success(stopped)
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun hasUsageStatsPermission(): Boolean {
        val appOpsManager = getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
        val mode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            appOpsManager.unsafeCheckOpNoThrow(
                AppOpsManager.OPSTR_GET_USAGE_STATS,
                android.os.Process.myUid(),
                applicationContext.packageName
            )
        } else {
            appOpsManager.checkOpNoThrow(
                AppOpsManager.OPSTR_GET_USAGE_STATS,
                android.os.Process.myUid(),
                applicationContext.packageName
            )
        }
        return mode == AppOpsManager.MODE_ALLOWED
    }

    private fun requestUsageStatsPermission() {
        val intent = Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS)
        startActivityForResult(intent, USAGE_STATS_PERMISSION_REQUEST_CODE)
    }

    private fun hasOverlayPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(this)
        } else {
            true  // Permission is granted by default before Android M
        }
    }

    @TargetApi(Build.VERSION_CODES.M)
    private fun requestOverlayPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                Uri.parse("package:$packageName"))
            startActivityForResult(intent, OVERLAY_PERMISSION_REQUEST_CODE)
        }
    }

    private fun startMonitoringService(): Boolean {
        if (!hasUsageStatsPermission() || !hasOverlayPermission()) {
            Log.w("MainActivity", "Cannot start service: missing required permissions")
            return false
        }

        // Save service state preference
        val prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().putBoolean("service_enabled", true).apply()

        val intent = Intent(this, AppUsageMonitorService::class.java)
        intent.action = AppUsageMonitorService.ACTION_START_MONITORING

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(intent)
        } else {
            startService(intent)
        }

        return true
    }

    private fun stopMonitoringService(): Boolean {
        // Save service state preference
        val prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().putBoolean("service_enabled", false).apply()

        val intent = Intent(this, AppUsageMonitorService::class.java)
        intent.action = AppUsageMonitorService.ACTION_STOP_MONITORING

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(intent)
        } else {
            startService(intent)
        }

        return true
    }

    @SuppressLint("QueryPermissionsNeeded")
    private fun getInstalledApps(): List<Map<String, String>> {
        val pm = packageManager
        val apps = mutableListOf<Map<String, String>>()

        val mainIntent = Intent(Intent.ACTION_MAIN, null)
        mainIntent.addCategory(Intent.CATEGORY_LAUNCHER)
        val resolvedInfos = pm.queryIntentActivities(mainIntent, 0)

        Log.d("MainActivity", "queryIntentActivities found ${resolvedInfos.size} potential launcher apps initially.")

        val allFoundPackages = mutableListOf<String>()
        for (resolveInfo in resolvedInfos) {
            try {
                val appInfo = resolveInfo.activityInfo.applicationInfo
                val packageName = appInfo.packageName
                allFoundPackages.add(packageName)

                if ((appInfo.flags and ApplicationInfo.FLAG_SYSTEM) == 0) {
                    val appName = pm.getApplicationLabel(appInfo).toString()
                    // Get the app icon as a base64 string
                    val icon = pm.getApplicationIcon(appInfo)
                    val bitmap = (icon as android.graphics.drawable.BitmapDrawable).bitmap
                    val byteArrayOutputStream = java.io.ByteArrayOutputStream()
                    bitmap.compress(android.graphics.Bitmap.CompressFormat.PNG, 100, byteArrayOutputStream)
                    val iconBytes = byteArrayOutputStream.toByteArray()
                    // Use NO_WRAP flag to ensure proper base64 encoding
                    val iconBase64 = android.util.Base64.encodeToString(iconBytes, android.util.Base64.NO_WRAP)

                    apps.add(mapOf(
                        "appName" to appName,
                        "packageName" to packageName,
                        "iconBase64" to iconBase64
                    ))
                }
            } catch (e: Exception) {
                Log.e("MainActivity", "Error processing resolveInfo: ${e.message}")
            }
        }

        Log.d("MainActivity", "All packages found by queryIntentActivities: ${allFoundPackages.joinToString()}")
        Log.d("MainActivity", "Found ${apps.size} non-system launcher apps (system filter applied)")
        return apps.sortedBy { it["appName"]?.lowercase() }
    }

    @SuppressLint("QueryPermissionsNeeded")
    private fun getUsageStats(days: Int): Map<String, Map<String, Any>> {
        Log.d("MainActivity", "Fetching usage stats for last $days days")
        val usageStatsMap = mutableMapOf<String, Map<String, Any>>()

        if (!hasUsageStatsPermission()) {
            Log.w("MainActivity", "Usage stats permission not granted")
            return usageStatsMap
        }

        // Instead of duplicating the logic, call getInstalledApps()
        val userInstalledAppsList = getInstalledApps()
        val installedApps = userInstalledAppsList.associate { it["packageName"]!! to it["appName"]!! }.toMutableMap()
        val appIcons = userInstalledAppsList.associate { it["packageName"]!! to it["iconBase64"] }.toMutableMap()
        Log.d("MainActivity", "Found ${installedApps.size} installed non-system apps via getInstalledApps().")

        // Then get usage stats
        val usageStatsManager = getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager
        val calendar = Calendar.getInstance()
        val endTime = calendar.timeInMillis
        calendar.add(Calendar.DAY_OF_YEAR, -days)
        val startTime = calendar.timeInMillis

        val usageStatsList = usageStatsManager.queryUsageStats(UsageStatsManager.INTERVAL_DAILY, startTime, endTime)
        Log.d("MainActivity", "UsageStatsManager returned ${usageStatsList?.size ?: 0} stats entries.")

        // First, process usage stats for apps that have usage
        usageStatsList?.forEach { stats ->
            val packageName = stats.packageName
            if (installedApps.containsKey(packageName)) {
                val timeInForeground = stats.totalTimeInForeground
                val appName = installedApps[packageName] ?: packageName

                val appStats: Map<String, Any> = mapOf(
                    "appName" to (appName as Any),
                    "packageName" to (packageName as Any),
                    "timeInForegroundMs" to (timeInForeground as Any),
                    "timeInMinutes" to ((timeInForeground / 60000) as Any),
                    "lastTimeUsed" to (stats.lastTimeUsed as Any),
                    "iconBase64" to (appIcons[packageName] as Any)
                )
                usageStatsMap[packageName] = appStats
            }
        }
        Log.d("MainActivity", "Processed ${usageStatsMap.size} apps with usage stats.")

        // Then add all installed apps that had no usage
        installedApps.forEach { (packageName, appName) ->
            if (!usageStatsMap.containsKey(packageName)) {
                val appStats: Map<String, Any> = mapOf(
                    "appName" to (appName as Any),
                    "packageName" to (packageName as Any),
                    "timeInForegroundMs" to (0L as Any),
                    "timeInMinutes" to (0 as Any),
                    "lastTimeUsed" to (0L as Any),
                    "iconBase64" to (appIcons[packageName] as Any)
                )
                usageStatsMap[packageName] = appStats
            }
        }

        Log.d("MainActivity", "Returning final usage stats map with ${usageStatsMap.size} apps.")
        return usageStatsMap
    }

    private fun isCommonSystemApp(packageName: String): Boolean {
        val commonApps = listOf(
            // Google apps
            "com.android.chrome",
            "com.google.android.youtube",
            "com.google.android.gm",
            "com.google.android.apps.photos",
            "com.google.android.apps.maps",
            "com.google.android.calendar",
            "com.android.camera",
            "com.google.android.apps.messaging",
            "com.google.android.googlequicksearchbox",
            "com.google.android.apps.docs",
            "com.google.android.keep",
            "com.google.android.music",
            "com.google.android.videos",
            "com.google.android.apps.tachyon", // Google Duo

            // Social media
            "com.facebook.katana", // Facebook
            "com.instagram.android",
            "com.twitter.android",
            "com.snapchat.android",
            "com.pinterest",
            "com.whatsapp",
            "com.linkedin.android",
            "com.discord",
            "com.reddit.frontpage",

            // Messaging
            "org.telegram.messenger",
            "com.facebook.orca", // Facebook Messenger
            "com.skype.raider",
            "kik.android",
            "jp.naver.line.android",

            // Entertainment
            "com.spotify.music",
            "com.netflix.mediaclient",
            "com.amazon.avod.thirdpartyclient", // Prime Video
            "com.disney.disneyplus",
            "com.hulu.plus",
            "com.tiktok.android",
            "tv.twitch.android.app",

            // Browsers
            "org.mozilla.firefox",
            "com.opera.browser",
            "com.microsoft.emmx" // Edge
        )
        return commonApps.contains(packageName)
    }

    private fun setAppLimit(packageName: String, limitMinutes: Int) {
        val prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val editor = prefs.edit()

        if (limitMinutes > 0) {
            editor.putInt(PREF_PREFIX_LIMIT + packageName, limitMinutes)
        } else {
            editor.remove(PREF_PREFIX_LIMIT + packageName)
        }

        editor.apply()

        // Immediately tell the service to reload limits
        val intent = Intent(this, AppUsageMonitorService::class.java)
        intent.action = AppUsageMonitorService.ACTION_RELOAD_LIMITS
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(intent)
        } else {
            startService(intent)
        }

        Log.d("MainActivity", "Set app limit for $packageName to $limitMinutes minutes and notified service")
    }

    private fun getAppLimits(): Map<String, Int> {
        val prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val result = mutableMapOf<String, Int>()

        for (entry in prefs.all) {
            if (entry.key.startsWith(PREF_PREFIX_LIMIT) && entry.value is Int) {
                val packageName = entry.key.substring(PREF_PREFIX_LIMIT.length)
                result[packageName] = entry.value as Int
            }
        }

        return result
    }
}

