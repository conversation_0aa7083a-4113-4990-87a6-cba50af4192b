class ApiToken {
  final String token;
  final String tokenType;
  final int expiresIn;

  ApiToken({
    required this.token,
    required this.tokenType,
    required this.expiresIn,
  });

  factory ApiToken.fromJson(Map<String, dynamic> json) {
    return ApiToken(
      token: json['access_token'] ?? json['token'] ?? '',
      tokenType: json['token_type'] ?? 'Bearer',
      expiresIn: json['expires_in'] ?? 86400, // Default to 24 hours
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'access_token': token,
      'token_type': tokenType,
      'expires_in': expiresIn,
    };
  }

  String get authorizationHeader => '$tokenType $token';
}
