package com.example.commitime_test

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import android.util.Log

class BlockingActivity : AppCompatActivity() {

    companion object {
        const val EXTRA_APP_NAME = "extra_app_name"
        const val EXTRA_LIMIT_MINUTES = "extra_limit_minutes"
        const val EXTRA_PACKAGE_NAME = "extra_package_name"
        private const val TAG = "BlockingActivity"
        private const val CHECK_INTERVAL_MS = 100L // Check extremely frequently - 10 times per second
    }

    private var blockedPackageName: String? = null
    private val handler = Handler(Looper.getMainLooper())
    private lateinit var checkRunnable: Runnable
    private var blockingActive = false
    private var attemptCount = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Log.d(TAG, "BlockingActivity onCreate() - Start")

        // Set window flags to ensure we stay on top of everything
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            window.attributes.layoutInDisplayCutoutMode =
                android.view.WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
        }

        window.addFlags(android.view.WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        window.addFlags(android.view.WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD)
        window.addFlags(android.view.WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED)
        window.addFlags(android.view.WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON)
        window.addFlags(android.view.WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS)
        window.addFlags(android.view.WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN)
        window.addFlags(android.view.WindowManager.LayoutParams.FLAG_FULLSCREEN)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            setShowWhenLocked(true)
            setTurnScreenOn(true)
        }

        setContentView(R.layout.activity_blocking)

        // Apply animation to the content
        val contentLayout = findViewById<android.view.View>(android.R.id.content)
        val animation = android.view.animation.AnimationUtils.loadAnimation(this, R.anim.slide_up)
        contentLayout.startAnimation(animation)

        // Apply pulse animation to the icon
        val imageView = findViewById<ImageView>(R.id.imageView)
        val pulseAnimation = android.view.animation.AnimationUtils.loadAnimation(this, R.anim.pulse)
        imageView.startAnimation(pulseAnimation)

        // Get the extras from the intent
        val appName = intent.getStringExtra(EXTRA_APP_NAME) ?: "This app"
        val limitMinutes = intent.getIntExtra(EXTRA_LIMIT_MINUTES, 0)
        blockedPackageName = intent.getStringExtra(EXTRA_PACKAGE_NAME)

        Log.d(TAG, "BlockingActivity created to block: $appName ($blockedPackageName) with limit: $limitMinutes min")

        // Set up the UI elements
        val titleText: TextView = findViewById(R.id.titleText)
        val appNameText: TextView = findViewById(R.id.appNameText)
        val messageText: TextView = findViewById(R.id.messageText)
        val dismissButton: Button = findViewById(R.id.dismissButton)
        val moreTimeButton: Button = findViewById(R.id.moreTimeButton)

        titleText.text = "TIME LIMIT REACHED"
        appNameText.text = appName
        messageText.text = "You've reached your daily limit of $limitMinutes minutes for today"

        dismissButton.setOnClickListener {
            Log.d(TAG, "Dismiss button clicked")
            // Make absolutely sure the blocked app is closed
            forceCloseBlockedApp()

            // Go to home
            goToHomeScreen()
        }

        moreTimeButton.setOnClickListener {
            Log.d(TAG, "More Time button clicked, navigating to ChatAiScreen")

            // Make absolutely sure the blocked app is closed
            forceCloseBlockedApp()

            // Launch the Flutter activity with the ChatAiScreen route and pass app info
            val chatIntent = Intent(this, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or
                        Intent.FLAG_ACTIVITY_CLEAR_TOP or
                        Intent.FLAG_ACTIVITY_CLEAR_TASK or
                        Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED
                putExtra("route", "/chat_ai")

                // Pass app name and usage time as extras
                val appName = findViewById<TextView>(R.id.appNameText).text.toString()
                val limitMinutes = findViewById<TextView>(R.id.messageText).text.toString()
                    .replace("You've reached your daily limit of ", "")
                    .replace(" minutes for today", "")
                    .toIntOrNull() ?: 0

                putExtra("app_name", appName)
                putExtra("usage_time", "$limitMinutes minutes")

                Log.d(TAG, "Passing app info to ChatAiScreen: $appName, $limitMinutes minutes")
            }

            // Make sure the app is brought to the foreground
            startActivity(chatIntent)

            // End this activity
            finish()
        }

        // Set up the continuous check with a more direct approach
        checkRunnable = Runnable {
            if (blockingActive) {
                // Make sure blocked app is not running
                forceCloseBlockedApp()

                // Force self to front if needed
                if (!isActivityInForeground()) {
                    bringToFront()
                }

                // Continue checking
                handler.postDelayed(checkRunnable, CHECK_INTERVAL_MS)
            }
        }

        Log.d(TAG, "BlockingActivity onCreate() - End")

        // Start active blocking
        blockingActive = true
        handler.post(checkRunnable)
    }

    private fun forceCloseBlockedApp() {
        val packageName = blockedPackageName ?: return

        try {
            Log.d(TAG, "Forcing close of $packageName (attempt #${++attemptCount})")
            val am = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager

            // Method 1: Kill background processes
            try {
                am.killBackgroundProcesses(packageName)
            } catch (e: Exception) {
                Log.e(TAG, "Error killing process: ${e.message}")
            }

            // Method 2: Remove tasks
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                try {
                    am.appTasks.forEach { task ->
                        if (task.taskInfo?.topActivity?.packageName == packageName) {
                            task.finishAndRemoveTask()
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error removing task: ${e.message}")
                }
            }

            // Method 3: For Android 10+, use a direct intent approach
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                try {
                    // Go home
                    val homeIntent = Intent(Intent.ACTION_MAIN)
                    homeIntent.addCategory(Intent.CATEGORY_HOME)
                    homeIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    startActivity(homeIntent)

                    // Very small delay
                    Thread.sleep(50)
                } catch (e: Exception) {
                    Log.e(TAG, "Error with home intent: ${e.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in forceCloseBlockedApp: ${e.message}")
        }
    }

    private fun isActivityInForeground(): Boolean {
        try {
            val am = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val tasks = am.appTasks

            for (task in tasks) {
                val info = task.taskInfo ?: continue
                val className = info.topActivity?.className ?: continue

                if (className == this.javaClass.name) {
                    return true
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking if activity is in foreground: ${e.message}")
        }
        return false
    }

    private fun bringToFront() {
        try {
            Log.d(TAG, "Bringing BlockingActivity to front")

            val intent = Intent(this, BlockingActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                addFlags(Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED)
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)

                // Include the extras
                putExtra(EXTRA_PACKAGE_NAME, blockedPackageName)
                putExtra(EXTRA_APP_NAME, findViewById<TextView>(R.id.appNameText).text)
                putExtra(EXTRA_LIMIT_MINUTES,
                    findViewById<TextView>(R.id.messageText).text.toString()
                        .replace("You've reached your daily limit of ", "")
                        .replace(" minutes for today", "")
                        .toIntOrNull() ?: 0)
            }

            startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Error bringing activity to front: ${e.message}")
        }
    }

    private fun goToHomeScreen() {
        blockingActive = false
        handler.removeCallbacks(checkRunnable)

        // Force close the blocked app one last time
        forceCloseBlockedApp()

        try {
            // Go to home screen
            val homeIntent = Intent(Intent.ACTION_MAIN)
            homeIntent.addCategory(Intent.CATEGORY_HOME)
            homeIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            startActivity(homeIntent)

            // Small delay to ensure home screen appears
            Thread.sleep(100)
        } catch (e: Exception) {
            Log.e(TAG, "Error going to home screen: ${e.message}")
        }

        finish()
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "BlockingActivity onResume()")

        // Make sure we're in blocking mode
        if (!blockingActive) {
            blockingActive = true
            handler.post(checkRunnable)
        }

        // Force-close the app we're blocking
        forceCloseBlockedApp()
    }

    override fun onPause() {
        super.onPause()
        Log.d(TAG, "BlockingActivity onPause()")

        // If we're still blocking, try to immediately come back to front
        if (blockingActive) {
            forceCloseBlockedApp()

            handler.postDelayed({
                if (blockingActive) {
                    bringToFront()
                }
            }, 50) // Very small delay
        }
    }

    override fun onDestroy() {
        Log.d(TAG, "BlockingActivity onDestroy()")

        // Clean up
        blockingActive = false
        handler.removeCallbacks(checkRunnable)
        super.onDestroy()
    }

    override fun onBackPressed() {
        // Don't allow back button to dismiss
        Log.d(TAG, "Back button pressed - forcing to use dismiss button")
        // Show a toast explaining they must use the dismiss button
        android.widget.Toast.makeText(
            this,
            "Please use the 'I Understand' button to exit",
            android.widget.Toast.LENGTH_SHORT
        ).show()
    }
}