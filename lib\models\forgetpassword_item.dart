import 'package:flutter/material.dart';
import 'package:commitime_test/models/forgetpassword_model.dart';

class ForgetpasswordItem {
  final List<Forgetpass> forgetpass = [
    Forgetpass(
        title: "Forgot Password?",
        subTitle: "Recover you password if you have forgot the password!",
        textTitle: "Email",
        textSubTitle: "Ex: <EMAIL>",
        icon: Icons.email,
        textInputType: TextInputType.emailAddress,
        textInputAction: TextInputAction.next),
    Forgetpass(
        title: "Forgot Password?",
        subTitle:
            "We have sent an email to your email account with a verification code!",
        textTitle: "Verification Code",
        textSubTitle: "EX: 123456",
        icon: Icons.verified_rounded,
        textInputType: TextInputType.number,
        textInputAction: TextInputAction.next),
    Forgetpass(
        title: "Forgot Password?",
        subTitle: "Set your new password to login into your account!",
        textTitle: "Enter New Password",
        textSubTitle: "*******",
        icon: Icons.lock,
        textInputType: TextInputType.visiblePassword,
        textInputAction: TextInputAction.done),
  ];
}
