import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:commitime_test/cubits/get_user_data/get_user_data_cubit.dart';

//import 'package:gradient_borders/gradient_borders.dart';
//import 'package:simple_gradient_text/simple_gradient_text.dart';
//import 'package:gradient_icon/gradient_icon.dart';
import 'package:commitime_test/screens/customengav.dart';
import 'package:commitime_test/screens/forget_password.dart';

//import 'package:test/screens/ngav.dart';
import 'package:commitime_test/screens/question.dart';
import 'package:commitime_test/service/authentication_service.dart';
import 'package:commitime_test/utils/shared_prefs.dart';

import 'package:commitime_test/widget/my_password_text_field.dart';
import 'package:commitime_test/widget/mytextfromfield.dart';

class LogIn extends StatefulWidget {
  const LogIn({super.key});

  @override
  State<LogIn> createState() => _LogInState();
}

class _LogInState extends State<LogIn> {
  GlobalKey<FormState> formstate = GlobalKey<FormState>();
  bool password = true;
  final TextEditingController email = TextEditingController();
  final TextEditingController password1 = TextEditingController();
  final AuthenticationService authenticationService =
      AuthenticationService(dio: Dio());
  Future logINAdult() async {
    try {
      final result = await authenticationService.logINAdult(
          email: email.text, password: password1.text);
      final token = result.token;

      log("Successfully logged in: ");
      log("User Name: ${result.firstName}" " ${result.lastName}");
      log("Email: ${result.email}");
      log("is Under 18: ${result.isUnder18}");
      if (result.isUnder18 == true) {
        log("Parent Email: ${result.parentEmail}");
      }

      // Save login status, token, name and email to shared preferences
      bool loginSaved = await SharedPrefs.setLoggedIn(true);
      bool tokenSaved = await SharedPrefs.setUserToken(token);
      bool nameSaved = await SharedPrefs.setUserName(result.firstName);
      bool emailSaved = await SharedPrefs.setUserEmail(result.email);

      if (!loginSaved || !tokenSaved || !nameSaved || !emailSaved) {
        log("Warning: Failed to save login state to shared preferences");
      }

      // Check if widget is still mounted before using context
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text("Successfully logged in!"),
          backgroundColor: Colors.green,
        ),
      );
      context
          .read<GetUserDataCubit>()
          .getUserData(name: result.firstName, email: result.email);
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) => CustomeNagv(
                  token: token,
                )),
      );
    } catch (e) {
      log("Failed to log in: ${e.toString()}");

      // Check if widget is still mounted before using context
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              "Enter correct email or password, Or check your internet connection"),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ListView(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 100,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Log In",
                  style: const TextStyle(
                      color: Color(0xffBF65EB),
                      fontSize: 32,
                      fontFamily: "Aclonica",
                      fontWeight: FontWeight.bold),
                ),
                const SizedBox(
                  height: 24,
                ),
                const Text(
                  "Login now to track all your expenses and income at a place!",
                  style: TextStyle(color: Colors.black, fontSize: 20),
                ),
                const SizedBox(
                  height: 32,
                ),
                Form(
                    key: formstate,
                    child: Column(
                      children: [
                        Mytextfromfield(
                          controller: email,
                          lable: "Email",
                          hintText: "Ex: <EMAIL>",
                          myIcon: Icons.email,
                          textInputType: TextInputType.emailAddress,
                          textInputAction: TextInputAction.send,
                        ),
                        const SizedBox(
                          height: 32,
                        ),
                        MyPasswordTextField(
                          controller: password1,
                          lable: "Password",
                          hintText: "******",
                          myIcon: Icons.lock,
                          password: true,
                          textInputAction: TextInputAction.send,
                          textInputType: TextInputType.visiblePassword,
                        ),
                        const SizedBox(
                          height: 8,
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 200),
                          child: TextButton(
                              onPressed: () {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => ForgetPassword(),
                                    ));
                              },
                              child: Text(
                                "Forgot Password?",
                                style: const TextStyle(
                                    color: Color(0xffBF65EB),
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold),
                              )),
                        ),
                        const SizedBox(
                          height: 32,
                        ),
                        Container(
                          height: 45,
                          width: double.infinity,
                          decoration: BoxDecoration(
                              color: Color(0xffBF65EB),
                              borderRadius: BorderRadius.circular(15)),
                          child: TextButton(
                              onPressed: () {
                                if (formstate.currentState!.validate()) {
                                  logINAdult();
                                }
                              },
                              child: const Text(
                                "Log In",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 18),
                              )),
                        ),
                        const SizedBox(
                          height: 32,
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Text(
                                "ــــــــــــــــــــــــــــــــــــــــ",
                                style: TextStyle(color: Colors.black),
                              ),
                              Text(
                                " OR ",
                                style: const TextStyle(
                                    color: Color(0xffBF65EB),
                                    fontWeight: FontWeight.bold,
                                    fontSize: 24,
                                    fontFamily: "Acionica"),
                              ),
                              const Text(
                                "ــــــــــــــــــــــــــــــــــــــــ",
                                style: TextStyle(color: Colors.black),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          height: 32,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Text(
                              "Don’t have an account? ",
                              style:
                                  TextStyle(color: Colors.black, fontSize: 16),
                            ),
                            GestureDetector(
                              onTap: () {
                                Navigator.push(context, MaterialPageRoute(
                                  builder: (context) {
                                    return const Question();
                                  },
                                ));
                              },
                              child: Text(
                                "Register",
                                style: const TextStyle(
                                    color: Color(0xffBF65EB),
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                    fontFamily: "Acionica"),
                              ),
                            )
                          ],
                        )
                      ],
                    ))
              ],
            ),
          ),
        ],
      ),
    );
  }
}
