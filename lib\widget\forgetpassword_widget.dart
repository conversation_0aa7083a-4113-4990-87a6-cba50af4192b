/*
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:test/models/forgetpassword_model.dart';
import 'package:test/screens/customengav.dart';
import 'package:test/screens/login.dart';
import 'package:test/service/forget_password_service.dart';
import 'package:test/widget/mytextfromfield.dart';

class ForgetpasswordWidget extends StatefulWidget {
  const ForgetpasswordWidget({
    super.key,
    required this.forgetpass,
    required this.pageController,
    required this.index,
    required this.totalPage,
  });

  final Forgetpass forgetpass;
  final PageController pageController;
  final int index;
  final int totalPage;

  @override
  State<ForgetpasswordWidget> createState() => _ForgetpasswordWidgetState();
}

class _ForgetpasswordWidgetState extends State<ForgetpasswordWidget> {
  final TextEditingController _textEditingController = TextEditingController();
  final ForgetPasswordService forgetPasswordService =
      ForgetPasswordService(dio: Dio());
  @override
  void dispose() {
    _textEditingController.dispose();
    super.dispose();
  }

  void _handleNavigation() async {
    // Validate the input before navigation
    if (_textEditingController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Please fill out the required Email field')),
      );
      return;
    }
    try {
      if (widget.index == widget.totalPage - 1) {
        await forgetPasswordService.resetPasswordFun(
            email: _textEditingController.text,
            otp: _textEditingController.text,
            password: _textEditingController.text);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Password reset successfully'),
          ),
        );
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => const CustomeNagv(),
          ),
        );
      } else {
        await forgetPasswordService.forgetPassword(
            email: _textEditingController.text);

        widget.pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text(e.toString())));
      log(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        leading: IconButton(
          onPressed: () {
            if (widget.index == 0) {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => const LogIn(),
                ),
              );
            } else {
              widget.pageController.previousPage(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            }
          },
          icon: const Icon(Icons.arrow_back, color: Colors.black),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 64),
        child: Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.forgetpass.title,
                style: const TextStyle(
                  color: Color(0xffBF65EB),
                  fontSize: 24,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                widget.forgetpass.subTitle,
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 18,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(height: 36),
              Mytextfromfield(
                controller: _textEditingController,
                lable: widget.forgetpass.textTitle,
                hintText: widget.forgetpass.textSubTitle,
                myIcon: widget.forgetpass.icon,
                textInputType: widget.forgetpass.textInputType,
                textInputAction: widget.forgetpass.textInputAction,
              ),
              const SizedBox(height: 36),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size(double.infinity, 60),
                  backgroundColor: const Color(0xffBF65EB),
                ),
                onPressed: _handleNavigation,
                child: Text(
                  widget.index == widget.totalPage - 1 ? "Confirm" : "Submit",
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 20,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}*/
