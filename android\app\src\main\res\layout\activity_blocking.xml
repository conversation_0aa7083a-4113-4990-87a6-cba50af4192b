<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/blocking_gradient_background"
    tools:context=".BlockingActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="28dp"
        android:background="@drawable/card_background"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:elevation="12dp">

        <ImageView
            android:id="@+id/imageView"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginBottom="24dp"
            android:src="@drawable/time_limit_icon"
            android:scaleType="fitCenter" />

        <TextView
            android:id="@+id/titleText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:gravity="center"
            android:text="TIME LIMIT REACHED"
            android:textColor="#BF65EB"
            android:textSize="24sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium" />

        <TextView
            android:id="@+id/appNameText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:gravity="center"
            android:text="You've reached your daily limit for this app"
            android:textColor="#333333"
            android:textSize="20sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif" />

        <TextView
            android:id="@+id/messageText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:gravity="center"
            android:text="Take a break and try again tomorrow"
            android:textColor="#666666"
            android:textSize="16sp"
            android:fontFamily="sans-serif-light" />

        <Button
            android:id="@+id/dismissButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/dismiss_button_ripple"
            android:paddingStart="24dp"
            android:paddingTop="14dp"
            android:paddingEnd="24dp"
            android:paddingBottom="14dp"
            android:text="I Understand"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:fontFamily="sans-serif-medium"
            android:elevation="4dp" />

        <Space
            android:layout_width="match_parent"
            android:layout_height="16dp" />

        <Button
            android:id="@+id/moreTimeButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/more_time_button_ripple"
            android:paddingStart="24dp"
            android:paddingTop="14dp"
            android:paddingEnd="24dp"
            android:paddingBottom="14dp"
            android:text="Request More Time"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:fontFamily="sans-serif-medium"
            android:elevation="4dp" />

    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="32dp"
        android:gravity="center"
        android:text="This app has been blocked to help you manage your screen time"
        android:textColor="#CCCCCC"
        android:textSize="16sp"
        android:fontFamily="sans-serif"
        android:shadowColor="#80000000"
        android:shadowDx="1"
        android:shadowDy="1"
        android:shadowRadius="2" />

</RelativeLayout>