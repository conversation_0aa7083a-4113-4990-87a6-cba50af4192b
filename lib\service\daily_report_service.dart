import 'dart:convert';
import 'dart:developer' as developer;
import 'package:dio/dio.dart';
import 'package:commitime_test/models/api_model/daily_report_model.dart';
import 'package:commitime_test/utils/shared_prefs.dart';

class DailyReportService {
  final Dio dio;
  final String baseUrl = "https://1984-197-35-7-0.ngrok-free.app/api/";
  final String dailyReportEndpoint = "send-daily-report";

  DailyReportService({required this.dio});

  /// Send daily usage report to the API
  /// [appUsageData] - Map containing app names as keys and usage minutes as values
  /// Only includes apps with usage > 1 minute
  Future<DailyReportModel> sendDailyReport(
      Map<String, int> appUsageData) async {
    try {
      // Get authentication token
      final token = await SharedPrefs.getToken();
      if (token == null || token.isEmpty) {
        throw Exception("Authentication token not found. Please login again.");
      }

      // Filter apps with usage > 1 minute and prepare app usage details
      final List<AppUsageDetail> appUsageDetails = [];
      int totalScreenTimeMinutes = 0;

      appUsageData.forEach((appName, minutes) {
        if (minutes > 1) {
          appUsageDetails.add(AppUsageDetail(
            app: appName,
            minutes: minutes,
          ));
          totalScreenTimeMinutes += minutes;
        }
      });

      // Convert app usage details to JSON string
      final String appUsageDetailsJson = jsonEncode(
        appUsageDetails.map((detail) => detail.toJson()).toList(),
      );

      // Prepare the request data
      final requestData = {
        'screen_time_minutes': totalScreenTimeMinutes.toString(),
        'app_usage_details': appUsageDetailsJson,
        'report_date':
            DateTime.now().toIso8601String().split('T')[0], // YYYY-MM-DD format
      };

      developer.log(
        "Sending daily usage report: $requestData",
        name: "DailyReportService",
      );

      // Set up headers for the request
      final options = Options(
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Cache-Control': 'no-cache',
          'User-Agent': 'CommitimeApp/1.0',
        },
      );

      // Send the data to the API
      final response = await dio.post(
        '$baseUrl$dailyReportEndpoint',
        data: requestData,
        options: options,
      );

      developer.log(
        "Daily report response: ${response.statusCode}, data=${response.data}",
        name: "DailyReportService",
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return DailyReportModel.fromJson(response.data);
      } else {
        throw Exception("Failed to send daily report: ${response.statusCode}");
      }
    } on DioException catch (e) {
      developer.log(
        "DioException in sendDailyReport: ${e.message}",
        name: "DailyReportService",
        error: e,
      );

      final String errorMessage = e.response?.data?['message'] ??
          e.message ??
          "Failed to send daily report. Please try again later.";
      throw Exception(errorMessage);
    } catch (e) {
      developer.log(
        "Exception in sendDailyReport: $e",
        name: "DailyReportService",
        error: e,
      );
      throw Exception(
          "An unexpected error occurred while sending daily report.");
    }
  }

  /// Send daily report with app usage statistics from usage service
  /// This method integrates with your existing app usage tracking
  Future<DailyReportModel> sendDailyReportFromUsageStats() async {
    try {
      // You can integrate this with your existing app usage service
      // For now, this is a placeholder that you can customize based on your usage tracking implementation

      // Example: Get usage data from your app usage service
      // final appUsageData = await AppUsageService.getTodayUsageData();

      // For demonstration, using empty map - replace with actual usage data
      final Map<String, int> appUsageData = {};

      return await sendDailyReport(appUsageData);
    } catch (e) {
      developer.log(
        "Exception in sendDailyReportFromUsageStats: $e",
        name: "DailyReportService",
        error: e,
      );
      rethrow;
    }
  }

  /// Helper method to format app usage data for API
  static Map<String, int> formatAppUsageData(
      Map<String, dynamic> rawUsageData) {
    final Map<String, int> formattedData = {};

    rawUsageData.forEach((key, value) {
      if (value is num) {
        final minutes = value.round();
        if (minutes > 1) {
          formattedData[key] = minutes;
        }
      }
    });

    return formattedData;
  }
}
