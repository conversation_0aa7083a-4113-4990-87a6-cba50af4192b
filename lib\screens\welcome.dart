import 'package:flutter/material.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';
import 'package:gradient_borders/gradient_borders.dart';
import 'package:commitime_test/screens/login.dart';
import 'package:commitime_test/screens/question.dart';

class Welcome extends StatelessWidget {
  const Welcome({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 10),
        child: Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Center(
                  child: SizedBox(
                height: 83,
                width: 77,
                child: Image.asset(
                  "assets/images/Logo.png",
                  fit: BoxFit.fill,
                  height: 83,
                  width: 77,
                ),
              )),
              const SizedBox(
                height: 64,
              ),
              const Text(
                "Wlcome to ",
                style: TextStyle(color: Colors.black, fontSize: 24),
              ),
              const SizedBox(
                height: 4,
              ),
              GradientText(
                "CommiTime Application",
                colors: const [Color(0xffe2a1ed), Color(0xffBF65EB)],
                gradientDirection: GradientDirection.ltr,
                gradientType: GradientType.linear,
                style: const TextStyle(
                    fontFamily: "Pacifico",
                    fontSize: 24,
                    fontWeight: FontWeight.w400),
              ),
              const SizedBox(
                height: 4,
              ),
              const Text(
                "A place that helps you control screen     time.",
                style: TextStyle(color: Colors.black, fontSize: 18),
              ),
              const SizedBox(
                height: 32,
              ),
              const Text(
                "Let’s Get Started...",
                style: TextStyle(color: Colors.black, fontSize: 20),
              ),
              const SizedBox(
                height: 32,
              ),
              Container(
                height: 45,
                width: double.infinity,
                decoration: BoxDecoration(
                    gradient: const LinearGradient(
                        colors: [Color(0xffe2a1ed), Color(0xffBF65EB)],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight),
                    borderRadius: BorderRadius.circular(15)),
                child: TextButton(
                    onPressed: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const LogIn(),
                          ));
                    },
                    child: const Text(
                      "Log In",
                      style: TextStyle(color: Colors.white, fontSize: 18),
                    )),
              ),
              const SizedBox(
                height: 16,
              ),
              Container(
                height: 45,
                width: double.infinity,
                decoration: const BoxDecoration(
                    color: Colors.white,
                    border: GradientBoxBorder(
                      gradient: LinearGradient(
                        colors: [Color(0xfffdcec3), Color(0xffe2a1ed)],
                      ),
                    ),
                    borderRadius: BorderRadius.all(Radius.circular(15))),
                child: TextButton(
                    onPressed: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const Question(),
                          ));
                    },
                    child: GradientText(
                      colors: const [Color(0xffe2a1ed), Color(0xffBF65EB)],
                      "Sign Up",
                      style: const TextStyle(fontSize: 18),
                      gradientDirection: GradientDirection.ltr,
                      gradientType: GradientType.linear,
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
