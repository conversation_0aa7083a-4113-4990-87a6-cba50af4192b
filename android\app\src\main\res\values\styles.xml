<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Theme applied to the Android Window while the process is starting when the OS's Dark Mode setting is off -->
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <!-- Show a splash screen on the activity. Automatically removed when
             the Flutter engine draws its first frame -->
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style>
    <!-- Theme applied to the Android Window as soon as the process has started.
         This theme determines the color of the Android Window while your
         Flutter UI initializes, as well as behind your Flutter UI while its
         running.

         This Theme is only used starting with V2 of Flutter's Android embedding. -->
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>

    <style name="BlockingActivityTheme" parent="Theme.AppCompat.NoActionBar">
        <!-- Make it full screen and prominent -->
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowBackground">@drawable/blocking_gradient_background</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.9</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <!-- Window handling properties -->
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <!-- Ensure it's not easily dismissible -->
        <item name="android:windowDisablePreview">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <!-- Extra priority flags -->
        <item name="android:immersive">true</item>
        <item name="android:windowShowWallpaper">false</item>
        <!-- Button styling -->
        <item name="colorButtonNormal">@color/colorPrimary</item>
        <item name="colorAccent">@color/colorAccent</item>
        <!-- Text appearance -->
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Button</item>
    </style>
</resources>
