class AppData {
  final String packageName;
  final String appName;
  int timeLimitMinutes;

  AppData({
    required this.packageName,
    required this.appName,
    this.timeLimitMinutes = 0,
  });

  Map<String, dynamic> toMap() {
    return {
      'packageName': packageName,
      'appName': appName,
      'timeLimitMinutes': timeLimitMinutes,
    };
  }

  factory AppData.fromMap(Map<String, dynamic> map) {
    return AppData(
      packageName: map['packageName'] as String,
      appName: map['appName'] as String,
      timeLimitMinutes: map['timeLimitMinutes'] as int? ?? 0,
    );
  }
}
