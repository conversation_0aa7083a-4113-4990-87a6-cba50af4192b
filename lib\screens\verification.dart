/*import 'package:flutter/material.dart';

//import 'package:gradient_borders/gradient_borders.dart';
//import 'package:simple_gradient_text/simple_gradient_text.dart';
import 'package:test/screens/customengav.dart';

//import 'package:test/screens/ngav.dart';
import 'package:test/widget/mytextfromfield.dart';

class Verification extends StatefulWidget {
  const Verification({super.key});

  @override
  State<Verification> createState() => _VerificationState();
}

class _VerificationState extends State<Verification> {
  GlobalKey<FormState> formstate = GlobalKey<FormState>();
  bool password = true;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 100,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Verification",
              style: const TextStyle(
                  color: Color(0xffBF65EB),
                  fontSize: 32,
                  fontFamily: "Aclonica",
                  fontWeight: FontWeight.bold),
            ),
            const SizedBox(
              height: 24,
            ),
            const Text(
              "We have sent an email to your email account with a verification code!",
              style: TextStyle(color: Colors.black, fontSize: 20),
            ),
            const SizedBox(
              height: 120,
            ),
            Form(
                key: formstate,
                child: Column(
                  children: [
                    Mytextfromfield(
                      controller: TextEditingController(),
                      lable: "Verification Code",
                      hintText: "EX: 123456",
                      myIcon: Icons.verified_rounded,
                      textInputType: TextInputType.number,
                      textInputAction: TextInputAction.send,
                    ),
                    const SizedBox(
                      height: 180,
                    ),
                    Container(
                      height: 55,
                      width: double.infinity,
                      decoration: BoxDecoration(
                          color: const Color(0xffBF65EB),
                          borderRadius: BorderRadius.circular(15)),
                      child: TextButton(
                          onPressed: () {
                            if (formstate.currentState!.validate()) {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const CustomeNagv(token: ,),
                                  ));
                            }
                          },
                          child: const Text(
                            "Confirm",
                            style: TextStyle(color: Colors.white, fontSize: 18),
                          )),
                    ),
                  ],
                ))
          ],
        ),
      ),
    );
  }
}*/
