import 'package:flutter/material.dart';
import 'package:commitime_test/models/onboarding_model.dart';

class OnBoardingWidget extends StatelessWidget {
  const OnBoardingWidget({super.key, required this.onBoarding});
  final OnBoarding onBoarding;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        body: Padding(
          padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
          child: Center(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    height: 300,
                    width: 300,
                    child: Center(
                      child: Image.asset(onBoarding.image),
                    ),
                  ),
                  SizedBox(
                    height: 64,
                  ),
                  Text(
                    onBoarding.mainText,
                    style: const TextStyle(
                        color: Colors.black,
                        fontSize: 24,
                        fontFamily: "Aclonica"),
                  ),
                  SizedBox(
                    height: 16,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      onBoarding.subText,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ]),
          ),
        ));
  }
}
