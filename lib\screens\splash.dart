import 'package:flutter/material.dart';
import 'package:commitime_test/screens/onboarding_screen.dart';

class Splash extends StatelessWidget {
  const Splash({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GestureDetector(
        onTap: () {
          Navigator.push(context, MaterialPageRoute(
            builder: (context) {
              return const OnBorading();
            },
          ));
        },
        child: Container(
          height: double.infinity,
          width: double.infinity,
          decoration: const BoxDecoration(
              gradient: LinearGradient(colors: [
            Color(0xffe2a1ed),
            Color(0xffBF65EB),
            Color(0xffe2a1ed),
          ], begin: Alignment.topLeft, end: Alignment.bottomRight)),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                "assets/images/Logo.png",
                color: Colors.white,
              ),
              const SizedBox(
                width: 15,
              ),
              const Text("Commi",
                  style: TextStyle(
                      fontFamily: "Pacifico",
                      color: Colors.white,
                      fontSize: 40,
                      fontWeight: FontWeight.w400)),
              const Text("T",
                  style: TextStyle(
                      fontFamily: "Pacifico",
                      color: Colors.white,
                      fontSize: 40,
                      fontWeight: FontWeight.w400)),
              const Text("ime",
                  style: TextStyle(
                      fontFamily: "Pacifico",
                      color: Colors.white,
                      fontSize: 40,
                      fontWeight: FontWeight.w400)),
            ],
          ),
        ),
      ),
    );
  }
}
