import 'package:flutter/material.dart';

class InfoStatistics extends StatelessWidget {
  const InfoStatistics({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 60),
      child: SizedBox(
        width: 350,
        height: 220,
        child: Stack(
          children: [
            Positioned(
              child: Container(
                decoration: const BoxDecoration(
                  color: Color(0xffF7E5FF),
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(20)),
                ),
                height: 70,
                width: 180,
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [Text("Total time Usage"), Text("4h   30m   44s")],
                ),
              ),
            ),
            Positioned(
              right: 2,
              child: Container(
                decoration: const BoxDecoration(
                  color: Color(0xffF7E5FF),
                  borderRadius:
                      BorderRadius.only(topRight: Radius.circular(20)),
                ),
                height: 70,
                width: 180,
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [Text("Total sessions Today"), Text("33")],
                ),
              ),
            ),
            Positioned(
              top: 75,
              child: Container(
                decoration: const BoxDecoration(
                  color: Color(0xffF7E5FF),
                ),
                height: 70,
                width: 180,
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [Text("Highest Daily usage"), Text("10h 33m 30s")],
                ),
              ),
            ),
            Positioned(
              top: 75,
              right: 2,
              child: Container(
                decoration: const BoxDecoration(
                  color: Color(0xffF7E5FF),
                ),
                height: 70,
                width: 180,
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [Text("daily Average Usage"), Text("90m")],
                ),
              ),
            ),
            Positioned(
              top: 150,
              child: Container(
                decoration: const BoxDecoration(
                  color: Color(0xffF7E5FF),
                  borderRadius:
                      BorderRadius.only(bottomLeft: Radius.circular(20)),
                ),
                height: 70,
                width: 180,
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [Text("Limit Hits"), Text("31")],
                ),
              ),
            ),
            Positioned(
              top: 150,
              right: 2,
              child: Container(
                decoration: const BoxDecoration(
                  color: Color(0xffF7E5FF),
                  borderRadius:
                      BorderRadius.only(bottomRight: Radius.circular(20)),
                ),
                height: 70,
                width: 180,
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [Text("App Launches  "), Text("31")],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
