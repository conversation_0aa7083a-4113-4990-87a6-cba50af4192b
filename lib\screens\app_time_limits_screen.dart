// import 'package:flutter/material.dart';
// import 'package:commitime_test/models/app_data.dart';
// import 'package:commitime_test/service/app_usage_service.dart';

// class AppTimeLimitsScreen extends StatefulWidget {
//   const AppTimeLimitsScreen({super.key});

//   @override
//   State<AppTimeLimitsScreen> createState() => _AppTimeLimitsScreenState();
// }

// class _AppTimeLimitsScreenState extends State<AppTimeLimitsScreen> {
//   List<AppData> _apps = [];
//   bool _isLoading = true;
//   bool _hasChanges = false;

//   @override
//   void initState() {
//     super.initState();
//     _loadApps();
//   }

//   Future<void> _loadApps() async {
//     setState(() => _isLoading = true);
//     try {
//       final installedApps = await AppUsageService.getInstalledApps();
//       final appLimits = await AppUsageService.getAppLimits();

//       setState(() {
//         _apps = installedApps.map((app) {
//           final packageName = app['packageName'] as String;
//           return AppData(
//             packageName: packageName,
//             appName: app['appName'] as String,
//             timeLimitMinutes: appLimits[packageName] ?? 0,
//           );
//         }).toList();
//         _isLoading = false;
//       });
//     } catch (e) {
//       setState(() => _isLoading = false);
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(content: Text('Failed to load apps: $e')),
//       );
//     }
//   }

//   Future<void> _saveChanges() async {
//     setState(() => _isLoading = true);
//     try {
//       for (final app in _apps) {
//         if (app.timeLimitMinutes > 0) {
//           await AppUsageService.setAppLimit(
//             app.packageName,
//             app.timeLimitMinutes,
//           );
//         }
//       }
//       await AppUsageService.reloadAppLimits();
//       setState(() {
//         _hasChanges = false;
//         _isLoading = false;
//       });
//       ScaffoldMessenger.of(context).showSnackBar(
//         const SnackBar(content: Text('Time limits saved successfully')),
//       );
//     } catch (e) {
//       setState(() => _isLoading = false);
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(content: Text('Failed to save changes: $e')),
//       );
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('App Time Limits'),
//         actions: [
//           if (_hasChanges)
//             IconButton(
//               icon: const Icon(Icons.save),
//               onPressed: _isLoading ? null : _saveChanges,
//             ),
//         ],
//       ),
//       body: _isLoading
//           ? const Center(child: CircularProgressIndicator())
//           : _apps.isEmpty
//               ? const Center(child: Text('No apps found'))
//               : ListView.builder(
//                   itemCount: _apps.length,
//                   itemBuilder: (context, index) {
//                     final app = _apps[index];
//                     return Card(
//                       margin: const EdgeInsets.symmetric(
//                         horizontal: 16,
//                         vertical: 8,
//                       ),
//                       child: ListTile(
//                         title: Text(app.appName),
//                         subtitle: Text(app.packageName),
//                         trailing: Row(
//                           mainAxisSize: MainAxisSize.min,
//                           children: [
//                             IconButton(
//                               icon: const Icon(Icons.timer),
//                               onPressed: () {
//                                 showDialog(
//                                   context: context,
//                                   builder: (context) => TimeLimitDialog(
//                                     app: app,
//                                     onTimeLimitChanged: (minutes) {
//                                       setState(() {
//                                         app.timeLimitMinutes = minutes;
//                                         _hasChanges = true;
//                                       });
//                                     },
//                                   ),
//                                 );
//                               },
//                             ),
//                             Text(
//                               app.timeLimitMinutes > 0
//                                   ? '${app.timeLimitMinutes}m'
//                                   : 'No limit',
//                               style: Theme.of(context).textTheme.bodyMedium,
//                             ),
//                           ],
//                         ),
//                       ),
//                     );
//                   },
//                 ),
//     );
//   }
// }

// class TimeLimitDialog extends StatefulWidget {
//   final AppData app;
//   final Function(int) onTimeLimitChanged;

//   const TimeLimitDialog({
//     super.key,
//     required this.app,
//     required this.onTimeLimitChanged,
//   });

//   @override
//   State<TimeLimitDialog> createState() => _TimeLimitDialogState();
// }

// class _TimeLimitDialogState extends State<TimeLimitDialog> {
//   late int _minutes;

//   @override
//   void initState() {
//     super.initState();
//     _minutes = widget.app.timeLimitMinutes;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return AlertDialog(
//       title: Text('Set time limit for ${widget.app.appName}'),
//       content: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Text(
//             'Set daily time limit in minutes',
//             style: Theme.of(context).textTheme.bodyMedium,
//           ),
//           const SizedBox(height: 16),
//           Slider(
//             value: _minutes.toDouble(),
//             min: 0,
//             max: 240,
//             divisions: 48,
//             label: '$_minutes minutes',
//             onChanged: (value) {
//               setState(() => _minutes = value.toInt());
//             },
//           ),
//           Text(
//             '$_minutes minutes',
//             style: Theme.of(context).textTheme.titleLarge,
//           ),
//         ],
//       ),
//       actions: [
//         TextButton(
//           onPressed: () => Navigator.pop(context),
//           child: const Text('Cancel'),
//         ),
//         FilledButton(
//           onPressed: () {
//             widget.onTimeLimitChanged(_minutes);
//             Navigator.pop(context);
//           },
//           child: const Text('Save'),
//         ),
//       ],
//     );
//   }
// }
