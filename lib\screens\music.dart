import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:commitime_test/widget/streak.dart';
import 'package:commitime_test/widget/usage_chart.dart';

class Music extends StatelessWidget {
  const Music({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        scrolledUnderElevation: 0,
        backgroundColor: Colors.white,
        leading: IconButton(
          onPressed: () {
            Navigator.pop(context);
          },
          icon: const Icon(Icons.arrow_back_ios, color: Color(0xffEC4CFF)),
        ),
        centerTitle: true,
        title: const Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
              iconSize: 40,
              icon: FaIcon(FontAwesomeIcons.music),
              color: Colors.blue,
              onPressed: null,
            ),
            Text(
              "Music",
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
            ),
          ],
        ),
        actions: [Streak(), const SizedBox(width: 16)],
      ),
      body: ListView(
        scrollDirection: Axis.vertical,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 30),
            child: SizedBox(
              width: 350,
              height: 220,
              child: Stack(
                children: [
                  Positioned(
                    child: Container(
                      decoration: const BoxDecoration(
                        color: Color(0xffF7E5FF),
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                        ),
                      ),
                      height: 70,
                      width: 180,
                      child: const Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text("Total time Usage"),
                          Text("4h   30m   44s"),
                        ],
                      ),
                    ),
                  ),
                  Positioned(
                    right: 2,
                    child: Container(
                      decoration: const BoxDecoration(
                        color: Color(0xffF7E5FF),
                        borderRadius: BorderRadius.only(
                          topRight: Radius.circular(20),
                        ),
                      ),
                      height: 70,
                      width: 180,
                      child: const Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [Text("Total sessions Today"), Text("33")],
                      ),
                    ),
                  ),
                  Positioned(
                    top: 75,
                    child: Container(
                      decoration: const BoxDecoration(color: Color(0xffF7E5FF)),
                      height: 70,
                      width: 180,
                      child: const Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text("Highest Daily usage"),
                          Text("10h 33m 30s"),
                        ],
                      ),
                    ),
                  ),
                  Positioned(
                    top: 75,
                    right: 2,
                    child: Container(
                      decoration: const BoxDecoration(color: Color(0xffF7E5FF)),
                      height: 70,
                      width: 180,
                      child: const Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [Text("daily Average Usage"), Text("90m")],
                      ),
                    ),
                  ),
                  Positioned(
                    top: 150,
                    child: Container(
                      decoration: const BoxDecoration(
                        color: Color(0xffF7E5FF),
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(20),
                        ),
                      ),
                      height: 70,
                      width: 180,
                      child: const Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [Text("Limit Hits"), Text("31")],
                      ),
                    ),
                  ),
                  Positioned(
                    top: 150,
                    right: 2,
                    child: Container(
                      decoration: const BoxDecoration(
                        color: Color(0xffF7E5FF),
                        borderRadius: BorderRadius.only(
                          bottomRight: Radius.circular(20),
                        ),
                      ),
                      height: 70,
                      width: 180,
                      child: const Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [Text("App Launches  "), Text("31")],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Divider(thickness: 2, height: 7, color: Color(0xffcc03cf)),
          ),
          const SizedBox(height: 8),
          const Center(
            child: Text(
              "11hrs, 20mins, 33swc screen time",
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(24.0),
            child: Container(height: 500, width: 315, child: UsageChart()),
          ),
        ],
      ),
    );
  }
}
