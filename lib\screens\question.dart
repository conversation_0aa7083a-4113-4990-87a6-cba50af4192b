import 'package:flutter/material.dart';
//import 'package:simple_gradient_text/simple_gradient_text.dart';
import 'package:commitime_test/screens/register.dart';
import 'package:commitime_test/screens/under18.dart';

class Question extends StatelessWidget {
  const Question({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 365,
                width: 400,
                child: Center(
                  child: Image.asset("assets/images/bro.png"),
                ),
              ),
              SizedBox(
                height: 24,
              ),
              Text(
                "Before we start",
                style: const TextStyle(
                    color: Colors.black,
                    fontSize: 24,
                    fontFamily: "Acionica",
                    fontWeight: FontWeight.bold),
              ),
              const SizedBox(
                height: 16,
              ),
              const Text(
                "Are you under 18 years old?",
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w300),
              ),
              const SizedBox(
                height: 64,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  ElevatedButton(
                      style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15))),
                      onPressed: () {
                        Navigator.push(context, MaterialPageRoute(
                          builder: (context) {
                            return const Register();
                          },
                        ));
                      },
                      child: const Text("No")),
                  ElevatedButton(
                      style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15))),
                      onPressed: () {
                        Navigator.push(context, MaterialPageRoute(
                          builder: (context) {
                            return const Under18();
                          },
                        ));
                      },
                      child: const Text("Yes"))
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
