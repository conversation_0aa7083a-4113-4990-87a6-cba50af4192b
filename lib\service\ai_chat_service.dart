import 'package:commitime_test/models/api_model/ai_analyze_emotion.dart';
import 'package:commitime_test/models/api_model/ai_chat_question.dart';
import 'package:commitime_test/models/api_model/ai_sugesstion.dart';
import 'package:commitime_test/models/api_model/authentication_model.dart';
import 'package:commitime_test/utils/shared_prefs.dart';
import 'package:dio/dio.dart';
import 'dart:developer' as developer;

class AiChatService {
  final Dio dio;
  final String baseUrl = "https://1984-197-35-7-0.ngrok-free.app/api/";
  final String questionEndpoint = "ai/generate-question";
  final String analyzeEmotionEndpoint = "ai/analyze-emotion";
  final String suggestEndpoint = "ai/suggest-activity"; // Updated endpoint
  final String loginEndpoint = "auth/login";

  AiChatService({required this.dio}) {
    // Initialize dio interceptors for token handling
    dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Check if we have a valid token
          final token = await SharedPrefs.getUserToken();
          if (token != null && token.isNotEmpty) {
            // Add token to request header
            options.headers['Authorization'] = 'Bearer $token';
            developer.log("Added token to request", name: "AiChatService");
          } else {
            developer.log("No token available for request",
                name: "AiChatService");
          }
          return handler.next(options);
        },
        onError: (DioException error, handler) async {
          // If we get a 401 Unauthorized error, try to refresh the token
          if (error.response?.statusCode == 401) {
            developer.log("Received 401 error, attempting to use default token",
                name: "AiChatService");

            // For testing purposes, use a default token
            // In a real app, you would implement a proper token refresh mechanism
            const defaultToken = "YOUR_DEFAULT_TOKEN_HERE";
            await SharedPrefs.setUserToken(defaultToken);

            // Retry the original request with the new token
            final opts = error.requestOptions;
            opts.headers['Authorization'] = 'Bearer $defaultToken';

            try {
              final response = await dio.fetch(opts);
              return handler.resolve(response);
            } catch (e) {
              developer.log("Error retrying request with default token: $e",
                  name: "AiChatService");
            }
          }
          return handler.next(error);
        },
      ),
    );
  }

  // Method to login and get a token
  Future<String?> login({
    required String email,
    required String password,
  }) async {
    try {
      developer.log("Attempting to login with email: $email",
          name: "AiChatService");

      final response = await dio.post(
        "$baseUrl$loginEndpoint",
        data: {
          'email': email,
          'password': password,
        },
      );

      if (response.statusCode == 200) {
        final authModel = AuthenticationModel.fromJson(response.data);

        // Save the token
        await SharedPrefs.setUserToken(authModel.token);
        developer.log("Login successful, token saved", name: "AiChatService");

        return authModel.token;
      } else {
        developer.log("Login failed: ${response.statusCode}",
            name: "AiChatService");
        return null;
      }
    } catch (e) {
      developer.log("Login error: $e", name: "AiChatService");
      return null;
    }
  }

  // Method to check if we have a valid token
  Future<bool> ensureAuthenticated() async {
    final token = await SharedPrefs.getUserToken();
    if (token != null && token.isNotEmpty) {
      developer.log("Valid token found", name: "AiChatService");
      return true;
    }

    // For testing purposes, use a default token
    // In a real app, you would redirect to login
    const defaultToken =
        "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2FjMjctMTk3LTM1LTc2LTE1My5uZ3Jvay1mcmVlLmFwcC9hcGkvYXV0aC9sb2dpbiIsImlhdCI6MTcxNzQyMzA1OCwiZXhwIjoxNzE3NDI2NjU4LCJuYmYiOjE3MTc0MjMwNTgsImp0aSI6IkRJRGJJRGJJRGJJRGJJRGJJRGJJRGIiLCJzdWIiOiIxIiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.YOUR_SIGNATURE_HERE";
    await SharedPrefs.setUserToken(defaultToken);
    developer.log("Using default token for testing", name: "AiChatService");
    return true;
  }

  Future<AiChatQuestion> getGenerateQuestion({
    required String appName,
    required String usageTime,
  }) async {
    // Ensure we have a valid token
    await ensureAuthenticated();

    try {
      // Log the request for debugging
      developer.log("Calling API with appName=$appName, usageTime=$usageTime",
          name: "AiChatService");

      // Set up headers for the request
      final options = Options(
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Cache-Control': 'no-cache',
          'User-Agent': 'CommitimeApp/1.0',
        },
      );

      // Make the API call
      Response response = await dio.post(
        "$baseUrl$questionEndpoint",
        data: {
          'app_name': appName,
          'usage_time': usageTime,
        },
        options: options,
      );

      // Log the response for debugging
      developer.log(
          "Response status=${response.statusCode}, data=${response.data}",
          name: "AiChatService");

      if (response.statusCode == 200) {
        // Handle the response based on its format
        if (response.data is Map<String, dynamic>) {
          // If it's already a map, use it directly
          return AiChatQuestion.fromjason(response.data);
        } else if (response.data is String) {
          // If it's a string, try to create a question object
          return AiChatQuestion(question: response.data);
        } else {
          // If it's neither, create a default question
          return AiChatQuestion(
              question:
                  "You've been enjoying $appName for $usageTime. Is there something else you'd like to spend some time on today?");
        }
      } else {
        developer.log("Error response: ${response.statusCode}",
            name: "AiChatService");
        throw Exception("Failed to generate question: ${response.statusCode}");
      }
    } on DioException catch (e) {
      // Log the error for debugging
      developer.log("DioException: ${e.message}, Response: ${e.response?.data}",
          name: "AiChatService");

      final String errMessage = e.response?.data?['message'] ??
          "Oops, there was an error while generating question. Try again later.";
      throw Exception(errMessage);
    } catch (e) {
      // Log any other errors
      developer.log("Unexpected error: $e", name: "AiChatService");
      throw Exception("Unexpected error: $e");
    }
  }

  Future<AiAnalyzeEmotion> getAnalyzeEmotion(
      {required String userResponse}) async {
    // Ensure we have a valid token
    await ensureAuthenticated();

    try {
      // Set up headers for the request
      final options = Options(
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Cache-Control': 'no-cache',
          'User-Agent': 'CommitimeApp/1.0',
        },
      );

      Response response = await dio.post(
        "$baseUrl$analyzeEmotionEndpoint",
        data: {
          'response': userResponse,
        },
        options: options,
      );

      developer.log(
          "Analyze emotion response: ${response.statusCode}, data=${response.data}",
          name: "AiChatService");

      if (response.statusCode == 200) {
        return AiAnalyzeEmotion.fromJson(response.data);
      } else {
        throw Exception("Failed to analyze emotion: ${response.statusCode}");
      }
    } on DioException catch (e) {
      developer.log(
          "DioException in analyze emotion: ${e.message}, Response: ${e.response?.data}",
          name: "AiChatService");

      final String errMessage = e.response?.data?['message'] ??
          "Oops, there was an error while analyzing emotion. Try again later.";
      throw Exception(errMessage);
    } catch (e) {
      developer.log("Unexpected error in analyze emotion: $e",
          name: "AiChatService");
      throw Exception("Unexpected error: $e");
    }
  }

  Future<AiSugesstion> getSuggestion({required String emtion}) async {
    // Ensure we have a valid token
    await ensureAuthenticated();

    try {
      // Set up headers for the request
      final options = Options(
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Cache-Control': 'no-cache',
          'User-Agent': 'CommitimeApp/1.0',
        },
      );

      try {
        // First try with the updated endpoint
        Response response = await dio.post(
          "$baseUrl$suggestEndpoint",
          data: {
            'emotion': emtion,
          },
          options: options,
        );

        developer.log(
            "Suggestion response: ${response.statusCode}, data=${response.data}",
            name: "AiChatService");

        if (response.statusCode == 200) {
          return AiSugesstion.fromJson(response.data);
        } else {
          throw Exception("Failed to get suggestion: ${response.statusCode}");
        }
      } on DioException catch (e) {
        // If the endpoint is not found, try the alternative endpoint
        if (e.response?.statusCode == 404) {
          developer.log("Endpoint not found, trying alternative endpoint",
              name: "AiChatService");

          // Try with the old endpoint as fallback
          Response response = await dio.post(
            "${baseUrl}ai/suggest",
            data: {
              'emotion': emtion,
            },
            options: options,
          );

          if (response.statusCode == 200) {
            return AiSugesstion.fromJson(response.data);
          }
        }

        // If we still have an error, use a fallback suggestion based on the emotion
        developer.log(
            "API unavailable, using fallback suggestion for emotion: $emtion",
            name: "AiChatService");

        // Provide a fallback suggestion based on the emotion
        String suggestion = _getFallbackSuggestion(emtion);
        return AiSugesstion(suggestion: suggestion);
      }
    } on DioException catch (e) {
      developer.log(
          "DioException in suggestion: ${e.message}, Response: ${e.response?.data}",
          name: "AiChatService");

      // Use fallback suggestion
      String suggestion = _getFallbackSuggestion(emtion);
      return AiSugesstion(suggestion: suggestion);
    } catch (e) {
      developer.log("Unexpected error in suggestion: $e",
          name: "AiChatService");

      // Use fallback suggestion
      String suggestion = _getFallbackSuggestion(emtion);
      return AiSugesstion(suggestion: suggestion);
    }
  }

  // Provide fallback suggestions based on emotion
  String _getFallbackSuggestion(String emotion) {
    // Convert to lowercase for case-insensitive matching
    final lowerEmotion = emotion.toLowerCase();

    // Map of emotions to fallback suggestions
    final Map<String, String> fallbackSuggestions = {
      'happy':
          "It's great that you're feeling positive! Consider channeling that energy into a creative activity or connecting with friends.",
      'sad':
          "I understand you might be feeling down. Taking a short walk outside or listening to uplifting music could help improve your mood.",
      'angry':
          "It seems you might be frustrated. Taking deep breaths or engaging in physical activity can help release tension.",
      'anxious':
          "I notice you might be feeling anxious. Try a brief mindfulness exercise or focusing on your breathing for a few minutes.",
      'neutral':
          "Perhaps you could try a new activity today that aligns with your goals or interests?",
      'excited':
          "Your enthusiasm is wonderful! This might be a good time to tackle something challenging that you've been putting off.",
      'tired':
          "It sounds like you might need some rest. Consider taking a short break or doing something relaxing.",
      'bored':
          "Maybe it's time to explore a new hobby or reconnect with an old interest you haven't pursued lately.",
      'confused':
          "When feeling uncertain, it can help to write down your thoughts or talk through them with someone you trust.",
      'stressed':
          "Taking a short break to do something you enjoy might help reduce your stress levels."
    };

    // Check if we have a specific suggestion for this emotion
    for (final key in fallbackSuggestions.keys) {
      if (lowerEmotion.contains(key)) {
        return fallbackSuggestions[key]!;
      }
    }

    // Default fallback suggestion
    return "Thank you for sharing. Consider taking a moment to reflect on what activities align with your values and goals right now.";
  }
}
