import 'package:flutter/material.dart';
import 'package:commitime_test/models/listtilemodel.dart';
import 'package:commitime_test/models/listtitleitem.dart';

class MyListTileWideget extends StatelessWidget {
  MyListTileWideget({
    super.key,
    required this.myListTile,
  });
  final MyListTile myListTile;
  final controller = ItemListTitle();

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: controller.itemlistTitle.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(top: 16),
          child: Container(
            height: 100,
            width: 345,
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(26)),
            child: ListTile(
              isThreeLine: true,
              contentPadding: const EdgeInsets.all(16),
              leading: Si<PERSON><PERSON>ox(
                  height: 40, width: 40, child: Image.asset(myListTile.image)),
              title: Text(myListTile.title),
              subtitle: SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  thumbShape: const RoundSliderThumbShape(
                      enabledThumbRadius: 0.0), // Remove thumb
                  thumbColor: Colors.transparent, // Make thumb invisible
                ),
                child: Slider(
                  thumbColor: Colors.white,
                  allowedInteraction: SliderInteraction.tapAndSlide,
                  activeColor: Colors.purpleAccent,
                  value: myListTile.slidervalue,
                  onChanged: (value) {},
                ),
              ),
              tileColor: Colors.black,
              trailing: Text(
                "${myListTile.slidervalue}%",
                style: const TextStyle(fontSize: 16),
              ),
            ),
          ),
        );
      },
    );
  }
}
