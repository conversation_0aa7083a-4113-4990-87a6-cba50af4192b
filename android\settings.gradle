pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        file("local.properties").withInputStream { properties.load(it) }
        def flutterSdkPath = properties.getProperty("flutter.sdk")
        assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
        return flutterSdkPath
    }()

    includeBuild("$flutterSdkPath/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
    //noinspection AndroidGradlePluginVersion
    id "com.android.application" version '8.9.1' apply false
    id "org.jetbrains.kotlin.android" version "1.8.22" apply false
    id 'org.gradle.toolchains.foojay-resolver-convention' version '0.8.0'
}

include ":app"
