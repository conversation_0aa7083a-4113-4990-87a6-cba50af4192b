import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:commitime_test/models/bardata.dart';

class My<PERSON>hart extends StatelessWidget {
  const MyChart({super.key, required this.weeklyHours});
  final List weeklyHours;
  @override
  Widget build(BuildContext context) {
    BarData myBarData = BarData(
        week1: weeklyHours[0],
        week2: weeklyHours[1],
        week3: weeklyHours[2],
        week4: weeklyHours[3],
        week5: weeklyHours[4],
        week6: weeklyHours[5],
        week7: weeklyHours[6],
        week8: weeklyHours[7]);
    myBarData.initializeBardata();
    return BarChart(BarChartData(
        gridData: FlGridData(show: false),
        borderData: FlBorderData(show: false),
        minY: 0,
        maxY: 168,
        barGroups: myBarData.barData
            .map((e) => BarChartGroupData(x: e.x, barRods: [
                  BarChartRodData(
                      toY: e.y,
                      borderRadius: BorderRadius.zero,
                      width: 14,
                      gradient: const LinearGradient(
                          colors: [Color(0xfffdcec3), Color(0xffe2a1ed)]))
                ]))
            .toList()));
  }
}
