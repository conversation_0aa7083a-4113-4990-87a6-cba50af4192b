import 'package:flutter/material.dart';
//import 'package:gradient_borders/input_borders/gradient_outline_input_border.dart';
//import 'package:gradient_icon/gradient_icon.dart';
//import 'package:simple_gradient_text/simple_gradient_text.dart';

// ignore: must_be_immutable
class Mytextfromfield extends StatelessWidget {
  final String lable;
  final String hintText;
  final IconData myIcon;
  final TextInputType textInputType;
  final TextInputAction textInputAction;
  TextEditingController controller;

  Mytextfromfield(
      {super.key,
      required this.lable,
      required this.hintText,
      required this.myIcon,
      required this.textInputType,
      required this.textInputAction,
      required this.controller});

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      keyboardType: TextInputType.name,
      textInputAction: TextInputAction.send,
      decoration: InputDecoration(
        label: Text(lable, style: const TextStyle(color: Color(0xffBF65EB))),
        hintText: hintText,
        hintStyle: const TextStyle(color: Colors.grey),
        labelStyle: const TextStyle(fontWeight: FontWeight.bold),
        prefix: Icon(
          myIcon,
          color: Color(0xffBF65EB),
        ),
        border: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Color(0xffBF65EB),
            ),
            borderRadius: BorderRadius.circular(15)),
      ),
      validator: (value) {
        if (value!.isEmpty) {
          return " This Field Can not Be Empty";
        }
        return null;
      },
    );
  }
}
