import 'package:flutter/material.dart';

class AccountListTile extends StatelessWidget {
  final IconData iconAvatar;
  final String title;
  final String subtitle;
  final IconData? iconTrailing;

  const AccountListTile(
      {super.key,
      required this.iconAvatar,
      required this.title,
      required this.subtitle,
      this.iconTrailing});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16),
      child: ListTile(
        isThreeLine: true,
        leading: CircleAvatar(
          radius: 24,
          child: Icon(
            iconAvatar,
            color: Color(0xffB929FE),
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
              color: Colors.black, fontSize: 18, fontWeight: FontWeight.w500),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
              color: Color(0xffABABAB),
              fontSize: 14,
              fontWeight: FontWeight.w400),
        ),
        trailing: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                iconTrailing,
                color: Colors.red,
              ),
              SizedBox(
                width: 12,
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey,
                size: 20,
              )
            ]),
      ),
    );
  }
}
