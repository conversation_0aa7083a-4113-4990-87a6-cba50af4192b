import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:commitime_test/screens/reset_password.dart';
import 'package:commitime_test/service/forget_password_service.dart';
import 'package:commitime_test/widget/mytextfromfield.dart';

class OtpScreen extends StatefulWidget {
  final String email;
  const OtpScreen({
    super.key,
    required this.email,
  });

  @override
  State<OtpScreen> createState() => _OtpScreenState();
}

class _OtpScreenState extends State<OtpScreen> {
  final TextEditingController _textEditingController = TextEditingController();
  final ForgetPasswordService forgetPasswordService =
      ForgetPasswordService(dio: Dio());
  Future getOtp() async {
    if (_textEditingController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill out the required Verification field'),
        ),
      );
      return;
    }
    try {
      final isValid = await forgetPasswordService.getOtp(
          email: widget.email, otp: _textEditingController.text.trim());
      if (isValid != null) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ResetPassword(
              email: widget.email,
              otp: _textEditingController.text.trim(),
            ),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(" Enter Otp code corecctly"),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(e.toString()),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        leading: IconButton(
          onPressed: () {
            Navigator.pop(context);
          },
          icon: Icon(Icons.arrow_back, color: Colors.black),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 64),
        child: Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Forgot Password?",
                style: const TextStyle(
                  color: Color(0xffBF65EB),
                  fontSize: 24,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                "We have sent an email to your email account with a verification code!",
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 18,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(height: 36),
              Mytextfromfield(
                controller: _textEditingController,
                lable: "Verification Code",
                hintText: "EX: 123456",
                myIcon: Icons.verified_rounded,
                textInputType: TextInputType.number,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 36),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size(double.infinity, 60),
                  backgroundColor: const Color(0xffBF65EB),
                ),
                onPressed: () {
                  getOtp();
                },
                child: Text(
                  "Submit",
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 20,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
