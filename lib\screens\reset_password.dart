import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:commitime_test/cubits/get_user_data/get_user_data_cubit.dart';
import 'package:commitime_test/screens/customengav.dart';
import 'package:commitime_test/service/authentication_service.dart';
import 'package:commitime_test/service/forget_password_service.dart';
import 'package:commitime_test/utils/shared_prefs.dart';
import 'package:commitime_test/widget/my_password_text_field.dart';

class ResetPassword extends StatefulWidget {
  final String email;
  final String otp;
  const ResetPassword({super.key, required this.email, required this.otp});

  @override
  State<ResetPassword> createState() => _ResetPasswordState();
}

class _ResetPasswordState extends State<ResetPassword> {
  bool password = true;

  final TextEditingController _textEditingController = TextEditingController();
  final ForgetPasswordService forgetPasswordService =
      ForgetPasswordService(dio: Dio());
  Future resetPassword() async {
    if (_textEditingController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill out the required Password field'),
        ),
      );
      return;
    }

    try {
      // Reset the password
      await forgetPasswordService.resetPasswordFun(
          email: widget.email,
          otp: widget.otp,
          password: _textEditingController.text.trim());

      // Login with the new password
      final result = await AuthenticationService(dio: Dio()).logINAdult(
          email: widget.email, password: _textEditingController.text.trim());

      // Save user data to SharedPrefs
      await SharedPrefs.setLoggedIn(true);
      await SharedPrefs.setUserToken(result.token);
      await SharedPrefs.setUserName(result.firstName);
      await SharedPrefs.setUserEmail(result.email);

      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Update the user data in the cubit
      context
          .read<GetUserDataCubit>()
          .getUserData(name: result.firstName, email: result.email);

      final token = result.token;

      // Navigate to the main screen
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CustomeNagv(
            token: token,
          ),
        ),
      );
    } catch (e) {
      // Check if widget is still mounted before using context
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to reset password: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        leading: IconButton(
          onPressed: () {
            Navigator.pop(context);
          },
          icon: Icon(Icons.arrow_back, color: Colors.black),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 64),
        child: Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Forgot Password?",
                style: const TextStyle(
                  color: Color(0xffBF65EB),
                  fontSize: 24,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                "Set your new password to login into your account!",
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 18,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(height: 36),
              MyPasswordTextField(
                  lable: "Enter New Password",
                  hintText: "*******",
                  myIcon: password ? Icons.visibility_off : Icons.visibility,
                  textInputType: TextInputType.visiblePassword,
                  textInputAction: TextInputAction.done,
                  password: password,
                  controller: _textEditingController),
              const SizedBox(height: 36),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size(double.infinity, 60),
                  backgroundColor: const Color(0xffBF65EB),
                ),
                onPressed: () {
                  resetPassword();
                },
                child: Text(
                  "Confirm",
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 20,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
