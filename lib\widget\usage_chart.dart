import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

class UsageChart extends StatelessWidget {
  const UsageChart({super.key});

  @override
  Widget build(BuildContext context) {
    return BarChart(
      BarChartData(
        barGroups: _chartData(),
        borderData: FlBorderData(show: false),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: true)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (double value, TitleMeta meta) {
                List<String> days = ["Sat", "Sun", "Mon", "Tue", "Wed", "Thu", "Fri"];
                return Text(days[value.toInt()], style: TextStyle(color: Colors.black));
              },
            ),
          ),
        ),
      ),
    );
  }

  List<BarChartGroupData> _chartData() {
    List<double> usageHours = [2, 6, 5, 4, 3, 2, 9]; // بيانات وهمية
    return List.generate(usageHours.length, (index) {
      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: usageHours[index],
             color: Color(0xffBF65EB),
            width: 15,
          ),
        ],
      );
    });
  }
}