import 'package:commitime_test/service/app_usage_service.dart';
import 'package:flutter/material.dart';

class PermissionsScreen extends StatefulWidget {
  final VoidCallback onPermissionsGranted;

  const PermissionsScreen({super.key, required this.onPermissionsGranted});

  @override
  State<PermissionsScreen> createState() => _PermissionsScreenState();
}

class _PermissionsScreenState extends State<PermissionsScreen> {
  bool _hasUsageStatsPermission = false;
  bool _hasOverlayPermission = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkAllPermissions();
  }

  Future<void> _checkAllPermissions() async {
    setState(() => _isLoading = true);

    final hasUsageStats = await AppUsageService.hasUsageStatsPermission();
    final hasOverlay = await AppUsageService.hasOverlayPermission();

    setState(() {
      _hasUsageStatsPermission = hasUsageStats;
      _hasOverlayPermission = hasOverlay;
      _isLoading = false;
    });

    // If both permissions are granted, start monitoring service
    if (_hasUsageStatsPermission && _hasOverlayPermission) {
      await AppUsageService.startMonitoringService();
      widget.onPermissionsGranted();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0xFFFFE5FF), Colors.white],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(height: 32),
                      Image.asset(
                        "assets/images/Logo.png",
                        height: 80,
                      ),
                      const SizedBox(height: 24),
                      const Text(
                        "App Permissions Required",
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        "Commitime needs the following permissions to help you monitor and manage your app usage time.",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 32),
                      _buildPermissionCard(
                        title: "Usage Access",
                        description:
                            "This allows us to track how much time you spend in each app.",
                        isGranted: _hasUsageStatsPermission,
                        onRequestPermission: _requestUsageStatsPermission,
                        icon: Icons.timer_outlined,
                      ),
                      const SizedBox(height: 16),
                      _buildPermissionCard(
                        title: "Display Over Other Apps",
                        description:
                            "This allows us to block apps when you've reached your time limit.",
                        isGranted: _hasOverlayPermission,
                        onRequestPermission: _requestOverlayPermission,
                        icon: Icons.layers_outlined,
                      ),
                      const Spacer(),
                      if (_hasUsageStatsPermission && _hasOverlayPermission)
                        ElevatedButton(
                          onPressed: () async {
                            await AppUsageService.startMonitoringService();
                            widget.onPermissionsGranted();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xffBF65EB),
                            minimumSize: const Size(double.infinity, 50),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text(
                            "Continue",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildPermissionCard({
    required String title,
    required String description,
    required bool isGranted,
    required VoidCallback onRequestPermission,
    required IconData icon,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xffF7E5FF),
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.all(12),
                child: Icon(
                  icon,
                  size: 24,
                  color: const Color(0xffBF65EB),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              isGranted
                  ? const Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 24,
                    )
                  : const SizedBox(width: 24),
            ],
          ),
          if (!isGranted) ...[
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: onRequestPermission,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xffBF65EB),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  "Grant Permission",
                  style: TextStyle(
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _requestUsageStatsPermission() async {
    await AppUsageService.requestUsageStatsPermission();
    // Check again after a delay to give the user time to grant the permission
    await Future.delayed(const Duration(seconds: 5));
    _checkAllPermissions();
  }

  Future<void> _requestOverlayPermission() async {
    await AppUsageService.requestOverlayPermission();
    // Check again after a delay to give the user time to grant the permission
    await Future.delayed(const Duration(seconds: 5));
    _checkAllPermissions();
  }
}
