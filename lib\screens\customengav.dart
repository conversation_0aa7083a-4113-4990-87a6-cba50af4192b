import 'package:commitime_test/widget/set_time_limit.dart';
import 'package:flutter/material.dart';
import 'package:commitime_test/screens/account.dart';
import 'package:commitime_test/screens/daschboard.dart';
import 'package:commitime_test/screens/report.dart';

import 'package:commitime_test/screens/statictics.dart';
import 'package:curved_navigation_bar/curved_navigation_bar.dart';

class CustomeNagv extends StatefulWidget {
  final String token;
  const CustomeNagv({super.key, required this.token});

  @override
  State<CustomeNagv> createState() => _CustomeNagvState();
}

class _CustomeNagvState extends State<CustomeNagv> {
  int currentPage = 0;
  final PageController controller = PageController();
  GlobalKey<CurvedNavigationBarState> bottomNavigationKey = GlobalKey();

  List<Widget> pageOption = [];

  @override
  void initState() {
    super.initState();
    pageOption = [
      const DashBoard(),
      const SetTimeLimit(),
      const Statictics(),
      Report(
        token: widget.token,
      ),
      const AccountPage(),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView(
        controller: controller,
        onPageChanged: (index) {
          setState(() {
            currentPage = index;
          });
        },
        children: pageOption,
      ),
      /*floatingActionButton: FloatingActionButton(
        onPressed: () {},
        backgroundColor: const Color(0xffBF65EB),
        tooltip: 'Add Time',
        child: const Icon(Icons.alarm_add_rounded, color: Colors.white),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,*/
      bottomNavigationBar: CurvedNavigationBar(
        key: bottomNavigationKey,
        index: currentPage,
        items: <Widget>[
          Icon(
            Icons.dashboard_sharp,
            size: 30,
            color: currentPage == 0 ? const Color(0xffAF52DE) : Colors.white,
          ),
          Icon(
            Icons.alarm_add_rounded,
            size: 30,
            color: currentPage == 1 ? const Color(0xffAF52DE) : Colors.white,
          ),
          Icon(
            Icons.show_chart_rounded,
            size: 30,
            color: currentPage == 2 ? const Color(0xffAF52DE) : Colors.white,
          ),
          Icon(
            Icons.bar_chart_rounded,
            size: 30,
            color: currentPage == 3 ? const Color(0xffAF52DE) : Colors.white,
          ),
          Icon(
            Icons.person,
            size: 30,
            color: currentPage == 4 ? const Color(0xffAF52DE) : Colors.white,
          ),
        ],
        color: Color(0xffBF65EB),
        buttonBackgroundColor: Color(0xffF6E4FF),
        backgroundColor: Colors.white,
        animationCurve: Curves.easeInOut,
        animationDuration: Duration(milliseconds: 600),
        onTap: (index) {
          setState(() {
            currentPage = index;
          });
          controller.jumpToPage(index);
        },
        letIndexChange: (currentPage) => true,
      ),

      /*BottomAppBar(
        color: const Color(0xffBF65EB),
        shape: const CircularNotchedRectangle(),
        notchMargin: 10.5,
        child: SizedBox(
          height: 60,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  navBarItem(Icons.dashboard_sharp, 'Dashboard', 0),
                  navBarItem(Icons.show_chart_rounded, 'Statistics', 1),
                ],
              ),
              const SizedBox(width: 48),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  navBarItem(Icons.bar_chart_rounded, 'Report', 2),
                  navBarItem(Icons.person, 'Profile', 3),
                ],
              ),
            ],
          ),
        ),
      ),*/
    );
  }

  Widget navBarItem(IconData icon, String label, int index) {
    return MaterialButton(
      minWidth: 40,
      onPressed: () {
        controller.jumpToPage(index);
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: currentPage == index ? Colors.white : Colors.grey.shade400,
          ),
          Text(
            label,
            style: TextStyle(
              color: currentPage == index ? Colors.white : Colors.grey.shade400,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}
