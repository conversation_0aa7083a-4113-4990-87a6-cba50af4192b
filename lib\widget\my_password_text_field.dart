import 'package:flutter/material.dart';

// ignore: must_be_immutable
class MyPasswordTextField extends StatefulWidget {
  final String lable;
  final String hintText;
  final IconData myIcon;
  final TextInputType textInputType;
  final TextInputAction textInputAction;
  bool password = true;
  TextEditingController controller;
  MyPasswordTextField(
      {super.key,
      required this.lable,
      required this.hintText,
      required this.myIcon,
      required this.textInputType,
      required this.textInputAction,
      required this.password,
      required this.controller});

  @override
  State<MyPasswordTextField> createState() => _MyPasswordTextFieldState();
}

class _MyPasswordTextFieldState extends State<MyPasswordTextField> {
  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      textInputAction: TextInputAction.send,
      keyboardType: TextInputType.visiblePassword,
      obscureText: widget.password,
      decoration: InputDecoration(
        suffixIcon: IconButton(
            onPressed: () {
              setState(() {
                if (widget.password = !widget.password) {}
              });
            },
            icon: Icon(
              widget.password
                  ? Icons.visibility_off_sharp
                  : Icons.visibility_sharp,
              color: Color(0xffBF65EB),
            )),
        label: Text(
          widget.lable,
        ),
        hintText: widget.hintText,
        hintStyle: const TextStyle(color: Colors.grey),
        labelStyle: const TextStyle(
            fontWeight: FontWeight.bold, color: Color(0xffBF65EB)),
        prefix: const Icon(
          Icons.lock_outline_rounded,
          color: Color(0xffBF65EB),
        ),
        border: OutlineInputBorder(
            borderSide: BorderSide(color: Color(0xffBF65EB)),
            borderRadius: BorderRadius.circular(15)),
      ),
      validator: (value) {
        if (value!.isEmpty) {
          return " This Field Can not Be Empty";
        }
        return null;
      },
    );
  }
}
