package com.example.commitime_test

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.app.usage.UsageEvents
import android.app.usage.UsageStatsManager
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import java.util.Calendar
import java.util.concurrent.TimeUnit
import androidx.core.app.NotificationCompat
import android.content.pm.PackageManager
import android.provider.Settings
import android.net.Uri
import android.content.ComponentName

class AppUsageMonitorService : Service() {

    private val CHANNEL_ID = "AppUsageMonitorServiceChannel"
    private val NOTIFICATION_ID = 1

    private lateinit var monitorHandler: Handler
    private lateinit var monitorRunnable: Runnable
    private val CHECK_INTERVAL_MS = 5000L // Check every 5 seconds

    private var lastForegroundApp: String? = null

    // SharedPreferences for reading limits set by MainActivity
    private lateinit var sharedPreferences: SharedPreferences
    private val PREFS_NAME = "AppUsageTrackerPrefs" // Same name as in MainActivity
    private val PREF_PREFIX_LIMIT = "limit_"      // Same prefix as in MainActivity
    private var currentAppLimits = mapOf<String, Int>() // Cache limits in memory

    // Keep track of which app is currently being blocked to avoid launching multiple overlays
    private var currentlyBlockedApp: String? = null

    companion object {
        private const val TAG = "AppUsageMonitorService"
        const val ACTION_START_MONITORING = "com.example.commitime_test.action.START_MONITORING"
        const val ACTION_STOP_MONITORING = "com.example.commitime_test.action.STOP_MONITORING"
        const val ACTION_RELOAD_LIMITS = "com.example.commitime_test.action.RELOAD_LIMITS"
        const val ACTION_START_SERVICE = "com.example.commitime_test.action.START_SERVICE"
        const val ACTION_APP_UNBLOCKED = "com.example.commitime_test.action.APP_UNBLOCKED"
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "Service Created")
        sharedPreferences = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        loadLimits() // Load limits initially

        createNotificationChannel()
        val notification = createNotification("Initializing monitor...")
        startForeground(NOTIFICATION_ID, notification)

        monitorHandler = Handler(Looper.getMainLooper())
        monitorRunnable = Runnable { // Define the task to run periodically
            checkForegroundApp()
            monitorHandler.postDelayed(monitorRunnable, CHECK_INTERVAL_MS)
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "Service Started with action: ${intent?.action}")

        // Reload limits in case they were changed while service was running
        loadLimits()

        when (intent?.action) {
             ACTION_START_MONITORING -> {
                 Log.i(TAG, "Starting monitoring loop...")
                 // Remove any existing callbacks before starting a new one
                 monitorHandler.removeCallbacks(monitorRunnable)
                 // Start the periodic check
                 monitorHandler.post(monitorRunnable)
                 updateNotification("Monitoring app usage...")
             }
             ACTION_STOP_MONITORING -> {
                 Log.i(TAG, "Stopping monitoring loop...")
                 monitorHandler.removeCallbacks(monitorRunnable)
                 stopForeground(true) // Remove notification
                 stopSelf() // Stop the service itself
             }
             ACTION_RELOAD_LIMITS -> {
                 Log.i(TAG, "Reloading app limits...")
                 loadLimits() // Reload limits from SharedPreferences
                 updateNotification("Monitoring app usage (limits updated)...")
             }
             ACTION_START_SERVICE -> {
                 Log.i(TAG, "Starting service...")
                 // If service restarts automatically (START_STICKY) and action is null,
                 // restart monitoring cautiously.
                 if (lastForegroundApp != null) { // Simple check if monitoring was likely active
                     monitorHandler.removeCallbacks(monitorRunnable)
                     monitorHandler.post(monitorRunnable)
                     updateNotification("Monitoring restarted...")
                 }
             }
             ACTION_APP_UNBLOCKED -> {
                 val packageName = intent.getStringExtra("package_name")
                 Log.i(TAG, "App unblocked action received for package: $packageName")
                 
                 if (packageName != null && packageName == currentlyBlockedApp) {
                     // Reset the blocked app state
                     currentlyBlockedApp = null
                     Log.d(TAG, "Reset blocking state for $packageName")
                     updateNotification("Monitoring app usage (app unblocked)...")
                 }
             }
             else -> {
                 Log.w(TAG, "Service started with unexpected or null action. Starting monitoring by default if not already running.")
                 // If service restarts automatically (START_STICKY) and action is null,
                 // restart monitoring cautiously.
                 if (lastForegroundApp != null) { // Simple check if monitoring was likely active
                     monitorHandler.removeCallbacks(monitorRunnable)
                     monitorHandler.post(monitorRunnable)
                     updateNotification("Monitoring restarted...")
                 }
             }
         }

        return START_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "Service Destroyed")
        // Ensure the handler callbacks are removed when the service is destroyed
        monitorHandler.removeCallbacks(monitorRunnable)
    }

    override fun onBind(intent: Intent): IBinder? {
        return null
    }

    private fun loadLimits() {
        val limits = mutableMapOf<String, Int>()
        sharedPreferences.all.forEach { (key, value) ->
            if (key.startsWith(PREF_PREFIX_LIMIT) && value is Int) {
                val packageName = key.substring(PREF_PREFIX_LIMIT.length)
                if (value > 0) { // Only store positive limits
                   limits[packageName] = value
                   Log.d(TAG, "Loaded limit for $packageName: $value minutes")
                } // Ignore limits of 0 or less
            }
        }
        
        if (limits.isEmpty()) {
            Log.w(TAG, "No app limits found in SharedPreferences. Please set limits in the UI.")
        }
        
        currentAppLimits = limits
        Log.d(TAG, "Loaded ${currentAppLimits.size} app limits into service: $currentAppLimits")
    }

    private fun checkForegroundApp() {
        Log.d(TAG, "checkForegroundApp() called") // Upgraded from verbose to debug for better visibility
        val usageStatsManager = getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager?
        if (usageStatsManager == null) {
            Log.e(TAG, "UsageStatsManager not available.")
            return
        }

        val time = System.currentTimeMillis()
        val queryStartTime = time - 60 * 1000 // Increase window to 60 seconds for better detection
        val usageEvents = usageStatsManager.queryEvents(queryStartTime, time)

        var currentForegroundApp: String? = null
        val event = UsageEvents.Event()

        while (usageEvents.hasNextEvent()) {
            usageEvents.getNextEvent(event)
            if (event.eventType == UsageEvents.Event.MOVE_TO_FOREGROUND) {
                currentForegroundApp = event.packageName
                Log.d(TAG, "Found MOVE_TO_FOREGROUND event for: $currentForegroundApp")
            }
        }

        // If we couldn't detect a foreground app, use last known
        if (currentForegroundApp == null && lastForegroundApp != null) {
            // Check if the last known app is still potentially in foreground
            val usageStats = usageStatsManager.queryUsageStats(
                UsageStatsManager.INTERVAL_DAILY,
                time - 60 * 1000, // Last minute
                time
            )
            val lastAppStats = usageStats.find { it.packageName == lastForegroundApp }
            if (lastAppStats != null && 
                (time - lastAppStats.lastTimeUsed) < 60 * 1000) {
                // Last app was used in the last minute, likely still foreground
                currentForegroundApp = lastForegroundApp
                Log.d(TAG, "Using last known foreground app: $currentForegroundApp")
            }
        }

        // Log current app limits for debugging
        Log.d(TAG, "Current app limits: $currentAppLimits")

        // Pre-check: If currently blocked app is detected in foreground, take immediate action
        if (currentForegroundApp != null && currentlyBlockedApp == currentForegroundApp) {
            // The app we're trying to block is in foreground, take immediate action to kill it
            Log.w(TAG, "Detected blocked app $currentForegroundApp in foreground! Taking immediate action.")
            launchBlockingActivity(currentForegroundApp, currentAppLimits[currentForegroundApp] ?: 0)
            return
        }

        // Critical fix: Check if this is an app we need to block, regardless of current blocking state
        if (currentForegroundApp != null) {
            val limitMinutes = currentAppLimits[currentForegroundApp]
            if (limitMinutes != null && limitMinutes > 0) {
                val usageMillisToday = getUsageToday(usageStatsManager, currentForegroundApp)
                val limitMillis = TimeUnit.MINUTES.toMillis(limitMinutes.toLong())
                
                Log.d(TAG, "Usage for $currentForegroundApp today: ${TimeUnit.MILLISECONDS.toMinutes(usageMillisToday)} minutes (${TimeUnit.MILLISECONDS.toSeconds(usageMillisToday)} seconds), Limit: $limitMinutes minutes")
                
                if (usageMillisToday > limitMillis) {
                    Log.w(TAG, "LIMIT EXCEEDED for $currentForegroundApp! Usage: ${TimeUnit.MILLISECONDS.toMinutes(usageMillisToday)} min, Limit: $limitMinutes min")
                    
                    // FORCE BLOCKING: Always launch blocking activity directly
                    Log.d(TAG, "Launching blocking activity directly due to exceeded limit")
                    launchBlockingActivity(currentForegroundApp, limitMinutes)
                    lastForegroundApp = currentForegroundApp // Set this as last app
                    return
                } else {
                    Log.d(TAG, "App $currentForegroundApp is under limit (${TimeUnit.MILLISECONDS.toMinutes(usageMillisToday)}/${limitMinutes})")
                }
            }
        }

        // Handle the case where the foreground app changed
        if (currentForegroundApp != null && currentForegroundApp != lastForegroundApp) {
            Log.i(TAG, "Foreground App Changed: $currentForegroundApp")
            lastForegroundApp = currentForegroundApp
            
            // Update blocking state - if we're not tracking this app anymore, clear blocked state
            if (currentForegroundApp != currentlyBlockedApp) {
                currentlyBlockedApp = null
            }
        }
    }

    private fun getUsageToday(usm: UsageStatsManager, packageName: String): Long {
        val calendar = Calendar.getInstance()
        val endTime = calendar.timeInMillis
        
        // Start time is beginning of day
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startTime = calendar.timeInMillis

        Log.d(TAG, "Checking usage for $packageName from ${startTime} to ${endTime}")

        // IMPORTANT: Always calculate from events for most accurate results
        val totalTimeMs = calculateUsageFromEvents(usm, packageName, startTime, endTime)
        
        // If we got a very small value from events calculation, try the older stats API as backup
        if (totalTimeMs < 10000) {
            Log.d(TAG, "Usage time from events seems low, trying stats API as backup")
            // Try different intervals if needed - some devices work better with different settings
            var usageStatsList = usm.queryUsageStats(UsageStatsManager.INTERVAL_DAILY, startTime, endTime)
            
            // If the list is empty, try with a larger interval
            if (usageStatsList.isNullOrEmpty()) {
                usageStatsList = usm.queryUsageStats(UsageStatsManager.INTERVAL_BEST, startTime, endTime)
            }
            
            // Find stats for our specific package
            val statsForApp = usageStatsList?.find { it.packageName == packageName }
            val statsTimeMs = statsForApp?.totalTimeInForeground ?: 0L
            
            // Use the larger of the two values to be safe
            val finalTimeMs = Math.max(totalTimeMs, statsTimeMs)
            Log.d(TAG, "Final usage time for $packageName: ${finalTimeMs/1000} seconds (events: ${totalTimeMs/1000}s, stats: ${statsTimeMs/1000}s)")
            return finalTimeMs
        }
        
        Log.d(TAG, "Usage for $packageName today from events: ${totalTimeMs / 1000} seconds")
        return totalTimeMs
    }
    
    private fun calculateUsageFromEvents(
        usm: UsageStatsManager, 
        packageName: String, 
        startTime: Long, 
        endTime: Long
    ): Long {
        var totalTimeInForeground = 0L
        var lastForegroundTimestamp: Long = 0
        var inForeground = false
        
        Log.d(TAG, "Calculating usage from events for $packageName")
        
        val events = usm.queryEvents(startTime, endTime)
        val event = UsageEvents.Event()
        var eventCount = 0
        var foregroundCount = 0
        var backgroundCount = 0
        
        while (events.hasNextEvent()) {
            events.getNextEvent(event)
            eventCount++
            
            if (event.packageName == packageName) {
                if (event.eventType == UsageEvents.Event.MOVE_TO_FOREGROUND) {
                    foregroundCount++
                    lastForegroundTimestamp = event.timeStamp
                    inForeground = true
                    Log.v(TAG, "Found FOREGROUND event at ${event.timeStamp}")
                } else if (event.eventType == UsageEvents.Event.MOVE_TO_BACKGROUND && inForeground) {
                    backgroundCount++
                    val foregroundTime = event.timeStamp - lastForegroundTimestamp
                    if (foregroundTime > 0) {
                        totalTimeInForeground += foregroundTime
                        Log.v(TAG, "Found BACKGROUND event at ${event.timeStamp}, adding ${foregroundTime/1000}s")
                    }
                    inForeground = false
                }
            }
        }
        
        Log.d(TAG, "Event statistics: total=$eventCount, foreground=$foregroundCount, background=$backgroundCount")
        
        // If the app is currently in foreground
        if (inForeground) {
            val currentTime = System.currentTimeMillis()
            val foregroundTime = currentTime - lastForegroundTimestamp
            if (foregroundTime > 0) {
                totalTimeInForeground += foregroundTime
                Log.d(TAG, "App is still in foreground, adding current session: ${foregroundTime/1000}s")
            }
        }
        
        Log.d(TAG, "Total calculated time from events for $packageName: ${totalTimeInForeground/1000} seconds")
        return totalTimeInForeground
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val serviceChannel = NotificationChannel(
                CHANNEL_ID,
                "App Usage Monitor Service Channel",
                NotificationManager.IMPORTANCE_LOW // Use LOW importance for less intrusive notification
            ).apply {
                description = "Channel for the background app usage monitoring service"
            }

            val manager = getSystemService(NotificationManager::class.java)
            manager?.createNotificationChannel(serviceChannel)
        }
    }

     private fun createNotification(text: String): Notification {
         // Intent to open the app when notification is tapped
         val notificationIntent = Intent(this, MainActivity::class.java)

         // Ensure flags are compatible with PendingIntent mutability requirements
         val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
         } else {
            PendingIntent.FLAG_UPDATE_CURRENT
         }

         val pendingIntent = PendingIntent.getActivity(this, 0, notificationIntent, pendingIntentFlags)

         return NotificationCompat.Builder(this, CHANNEL_ID)
             // Ensure you have an 'ic_launcher' in your mipmap folders,
             // or replace with a dedicated notification icon.
             .setSmallIcon(R.mipmap.ic_launcher)
             .setContentTitle("Usage Monitor") // Simplified title
             .setContentText(text)
             .setContentIntent(pendingIntent)
             .setOngoing(true) // Makes the notification non-dismissable while service is foreground
             .setOnlyAlertOnce(true) // Prevents sound/vibration on subsequent updates
             .build()
    }

    private fun updateNotification(text: String) {
        val notification = createNotification(text)
        val manager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        manager.notify(NOTIFICATION_ID, notification)
    }

    // --- Direct blocking method using overlay service ---
    private fun launchBlockingActivity(packageName: String, limitMinutes: Int) {
        // Check overlay permission before attempting to launch
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
            Log.e(TAG, "Cannot launch blocking overlay: SYSTEM_ALERT_WINDOW permission not granted.")
            // Update notification to alert user
            updateNotification("Error: Overlay permission needed to block apps! Tap to configure.")
            return
        }

        try {
            // Set this app as being blocked
            Log.d(TAG, "Setting $packageName as currently blocked app")
            currentlyBlockedApp = packageName
            
            // Get user-friendly app name 
            val pm = applicationContext.packageManager
            val appInfo = pm.getApplicationInfo(packageName, 0)
            val appName = pm.getApplicationLabel(appInfo).toString()
            
            Log.d(TAG, "Blocking $appName ($packageName) with limit of $limitMinutes minutes")
            
            // Go to home screen first to push app to background
            val homeIntent = Intent(Intent.ACTION_MAIN)
            homeIntent.addCategory(Intent.CATEGORY_HOME)
            homeIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            startActivity(homeIntent)
            
            // Short delay to ensure transition
            Thread.sleep(100)
            
            // Start the blocking overlay service
            val blockIntent = Intent(this, BlockingOverlayService::class.java).apply {
                putExtra(BlockingOverlayService.EXTRA_APP_NAME, appName)
                putExtra(BlockingOverlayService.EXTRA_PACKAGE_NAME, packageName)
                putExtra(BlockingOverlayService.EXTRA_LIMIT_MINUTES, limitMinutes)
            }
            
            // Start the service based on Android version
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(blockIntent)
            } else {
                startService(blockIntent)
            }
            
            // Update notification
            updateNotification("⚠️ BLOCKED: $appName - Time limit reached!")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error launching blocking overlay: ${e.message}", e)
            currentlyBlockedApp = null
        }
    }

} 