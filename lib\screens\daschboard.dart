import 'package:commitime_test/screens/chat_ai.dart';
import 'package:commitime_test/widget/daily_report_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:percent_indicator/percent_indicator.dart';
import 'package:commitime_test/cubits/get_user_data/get_user_data_cubit.dart';
import 'package:commitime_test/models/user_cubit_model.dart';

import 'package:commitime_test/service/app_usage_service.dart';
import 'package:commitime_test/widget/apps_list_tile.dart';

import 'package:commitime_test/widget/streak.dart';

class DashBoard extends StatefulWidget {
  const DashBoard({super.key});

  @override
  State<DashBoard> createState() => _DashBoardState();
}

class _DashBoardState extends State<DashBoard> {
  // App usage data
  List<AppUsageData> _usageData = [];
  double _totalUsageTimeToday = 0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUsageData();
  }

  Future<void> _loadUsageData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get today's usage data
      final usageData = await AppUsageService.getUsageStats(1);

      // Calculate total usage time
      double totalMs = 0;
      for (var app in usageData) {
        totalMs += app.timeInForegroundMs;
      }

      setState(() {
        _usageData = usageData;
        _totalUsageTimeToday = totalMs;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading app usage data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _formatDuration(double milliseconds) {
    final totalSeconds = milliseconds ~/ 1000;
    final hours = totalSeconds ~/ 3600;
    final minutes = (totalSeconds % 3600) ~/ 60;

    return '$hours h : $minutes m';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        scrolledUnderElevation: 0,
        leading: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 10),
          child: Image.asset("assets/images/Logo.png"),
        ),
        actions: [
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 10),
              child: Row(
                children: [
                  Streak(),
                  const SizedBox(
                    width: 10,
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ChatAiScreen(),
                          ));
                    },
                    child: Image.asset("assets/images/aichat.png"),
                  )
                ],
              )),
        ],
      ),
      drawer: Container(
          color: Colors.white,
          width: 200,
          child: const Column(
            children: [],
          )),
      body: BlocBuilder<GetUserDataCubit, UserCubitModel?>(
          builder: (context, state) {
        return _isLoading
            ? const Center(
                child: CircularProgressIndicator(color: Color(0xffBF65EB)))
            : RefreshIndicator(
                onRefresh: _loadUsageData,
                color: const Color(0xffBF65EB),
                child: ListView(children: [
                  Center(
                    child: Column(children: [
                      Padding(
                        padding: EdgeInsets.symmetric(vertical: 10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "Hello,",
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 24,
                              ),
                            ),
                            Text(
                              state != null && state.name.isNotEmpty
                                  ? state.name[0].toUpperCase() +
                                      state.name.substring(1)
                                  : "User",
                              style: TextStyle(
                                color: Color(0xffBF65EB),
                                fontSize: 24,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        height: 36,
                        width: 230,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(7),
                            color: const Color(0xfffbf3ff)),
                        child: const Center(
                          child: Text(
                            "screen total time",
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 24,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 32,
                      ),
                      Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: CircularPercentIndicator(
                          restartAnimation: true,
                          arcType: ArcType.FULL,
                          circularStrokeCap: CircularStrokeCap.round,
                          footer: const Stack(),
                          backgroundColor: Colors.grey,
                          linearGradient: const LinearGradient(
                            colors: [Color(0xffe2a1ed), Color(0xffBF65EB)],
                          ),
                          lineWidth: 20,
                          widgetIndicator: Stack(
                            children: [
                              Center(
                                child: Positioned(
                                  top: 100,
                                  left: 100,
                                  child: const Icon(
                                    size: 45,
                                    Icons.circle_rounded,
                                    color: Color(0xffBF65EB),
                                  ),
                                ),
                              ),
                              Center(
                                child: Positioned(
                                  top: 100,
                                  left: 100,
                                  child: const Icon(
                                    size: 20,
                                    Icons.circle_rounded,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          animation: true,
                          radius: 120,
                          percent: _calculateUsagePercent(),
                          startAngle: 45,
                          center: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    _formatDuration(_totalUsageTimeToday),
                                    style: const TextStyle(
                                        fontSize: 24,
                                        fontWeight: FontWeight.w600),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 32,
                              ),
                              Text(
                                "Today Time",
                                style: TextStyle(
                                    fontSize: 14, fontWeight: FontWeight.w500),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 16,
                      ),
                      Container(
                        width: double.infinity,
                        decoration: const BoxDecoration(
                            boxShadow: [
                              BoxShadow(
                                blurRadius: 20,
                                color: Colors.grey,
                                blurStyle: BlurStyle.normal,
                                spreadRadius: 0,
                              )
                            ],
                            color: Color(0xffF7E5FF),
                            borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(45),
                                topRight: Radius.circular(45))),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 32, vertical: 26),
                          child: Center(
                            child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16, vertical: 10),
                                    height: 387,
                                    width: 341,
                                    decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius:
                                            BorderRadius.circular(62)),
                                    child: Center(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          const Text(
                                            "Weekly OverView",
                                            style: TextStyle(
                                                fontSize: 16,
                                                color: Colors.black),
                                          ),
                                          const SizedBox(
                                            height: 16,
                                          ),
                                          SizedBox(
                                              height: 250,
                                              child: WeeklyUsageChart(
                                                  usageData: _usageData)),
                                        ],
                                      ),
                                    ),
                                  ),
                                  const SizedBox(
                                    height: 32,
                                  ),
                                  const Text(
                                    "Most Used Apps:",
                                    style: TextStyle(
                                      fontSize: 24,
                                      color: Colors.black,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  ..._usageData.take(3).map((app) {
                                    final percentage = app.timeInForegroundMs /
                                        _totalUsageTimeToday;

                                    return AppsListTile(
                                      pathImage:
                                          _getAppIconPath(app.packageName),
                                      title: app.appName,
                                      slidervalue:
                                          percentage > 1.0 ? 1.0 : percentage,
                                      time: app.formattedDuration,
                                      color: Colors.white,
                                      onTap: () {},
                                      iconBase64: app.iconBase64,
                                    );
                                  }),
                                ]),
                          ),
                        ),
                      ),
                    ]),
                  ),
                ]),
              );
      }),
    );
  }

  String _getAppIconPath(String packageName) {
    if (packageName.contains('facebook')) {
      return "assets/images/Facebook.png";
    } else if (packageName.contains('instagram')) {
      return "assets/images/Group 14.png";
    } else if (packageName.contains('youtube')) {
      return "assets/images/youtube.png";
    } else {
      return "assets/images/Logo.png";
    }
  }

  double _calculateUsagePercent() {
    // Use 24 hours (86,400,000 milliseconds) as the maximum usage time
    const maxUsageMs = 86400000.0; // 24 hours in milliseconds
    final percent = _totalUsageTimeToday / maxUsageMs;
    return percent > 1.0 ? 1.0 : percent;
  }
}

class WeeklyUsageChart extends StatelessWidget {
  final List<AppUsageData> usageData;

  const WeeklyUsageChart({super.key, required this.usageData});

  @override
  Widget build(BuildContext context) {
    // Calculate usage for each day of the week
    Map<String, double> dailyUsage = _calculateDailyUsage();

    // Maximum usage across all days (for normalization)
    double maxDailyUsage = dailyUsage.values.isEmpty
        ? 1.0
        : dailyUsage.values.reduce((a, b) => a > b ? a : b);

    // If max is 0, set it to 1 to avoid division by zero
    maxDailyUsage = maxDailyUsage > 0 ? maxDailyUsage : 1.0;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildDayBar('Mon', dailyUsage['Mon']! / maxDailyUsage,
            actualUsage: dailyUsage['Mon']!),
        _buildDayBar('Tue', dailyUsage['Tue']! / maxDailyUsage,
            actualUsage: dailyUsage['Tue']!),
        _buildDayBar('Wed', dailyUsage['Wed']! / maxDailyUsage,
            actualUsage: dailyUsage['Wed']!),
        _buildDayBar('Thu', dailyUsage['Thu']! / maxDailyUsage,
            actualUsage: dailyUsage['Thu']!),
        _buildDayBar('Fri', dailyUsage['Fri']! / maxDailyUsage,
            actualUsage: dailyUsage['Fri']!),
        _buildDayBar('Sat', dailyUsage['Sat']! / maxDailyUsage,
            actualUsage: dailyUsage['Sat']!),
        _buildDayBar('Sun', dailyUsage['Sun']! / maxDailyUsage,
            actualUsage: dailyUsage['Sun']!),
      ],
    );
  }

  // Calculate total usage time for each day of the week
  Map<String, double> _calculateDailyUsage() {
    // Initialize with zero values for each day
    Map<String, double> dailyUsage = {
      'Mon': 0.0,
      'Tue': 0.0,
      'Wed': 0.0,
      'Thu': 0.0,
      'Fri': 0.0,
      'Sat': 0.0,
      'Sun': 0.0,
    };

    // Get the current date and calculate dates for the past week
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // Create a map of dates for the past week
    Map<String, DateTime> weekDates = {};
    for (int i = 6; i >= 0; i--) {
      final date = today.subtract(Duration(days: i));
      final dayAbbr = _getDayAbbreviation(date.weekday);
      weekDates[dayAbbr] = date;
    }

    // Group usage data by day of week
    for (var app in usageData) {
      // Get the day of week for this app's usage
      final DateTime usageDate =
          DateTime.fromMillisecondsSinceEpoch(app.lastTimeUsed.toInt());
      final usageDay = DateTime(usageDate.year, usageDate.month, usageDate.day);

      // Find which day of the week this usage belongs to
      for (var entry in weekDates.entries) {
        final dayAbbr = entry.key;
        final date = entry.value;

        // If the usage date matches this day of the week
        if (usageDay.year == date.year &&
            usageDay.month == date.month &&
            usageDay.day == date.day) {
          // Add this app's usage time to the appropriate day
          dailyUsage[dayAbbr] =
              (dailyUsage[dayAbbr] ?? 0) + app.timeInForegroundMs;
          break;
        }
      }
    }

    return dailyUsage;
  }

  Widget _buildDayBar(String day, double value, {required double actualUsage}) {
    // Get current day of week
    final now = DateTime.now();
    final currentDay = _getDayAbbreviation(now.weekday);
    final isCurrentDay = day.toLowerCase() == currentDay.toLowerCase();

    // Make the bar height relate to the CircularPercentIndicator size (radius 120)
    final double maxHeight = 90; // Match the CircularPercentIndicator radius
    double barHeight;

    if (isCurrentDay) {
      // Current day: full height based on value
      barHeight = maxHeight * value;
      // Ensure a minimum height for visibility
      if (barHeight < 20 && value > 0) barHeight = 20;
    } else {
      // Inactive days: always have some height (min 30% of max height)
      // This ensures bars are visible even with no usage
      final double minInactiveHeight = maxHeight * 0.1; // 30% of max height

      // Scale the remaining 70% based on usage value
      barHeight =
          minInactiveHeight + ((maxHeight - minInactiveHeight) * value * 0.5);
    }

    // Format the actual usage time for the tooltip
    final String formattedUsage = _formatUsageTime(actualUsage);

    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Tooltip(
          message: '$day: $formattedUsage',
          child: Container(
            width: 25, // Slightly wider bars
            height: barHeight,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              gradient: LinearGradient(
                begin: Alignment.bottomCenter,
                end: Alignment.topCenter,
                colors: isCurrentDay
                    ? [const Color(0xffBF65EB), const Color(0xffe2a1ed)]
                    : [Colors.grey.shade400, Colors.grey.shade300],
              ),
              // Add a subtle shadow for better visibility
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(0, 0, 0, 0.1),
                  blurRadius: 2,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 5),
        Text(
          day,
          style: TextStyle(
            color: isCurrentDay ? const Color(0xffBF65EB) : Colors.grey,
            fontWeight: isCurrentDay ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ],
    );
  }

  // Format milliseconds to a readable time string (e.g., "2h 15m")
  String _formatUsageTime(double milliseconds) {
    final int totalMinutes = (milliseconds / (1000 * 60)).round();
    final int hours = totalMinutes ~/ 60;
    final int minutes = totalMinutes % 60;

    if (hours > 0) {
      return minutes > 0 ? '${hours}h ${minutes}m' : '${hours}h';
    } else {
      return '${minutes}m';
    }
  }

  // Helper method to get day abbreviation from weekday number
  String _getDayAbbreviation(int weekday) {
    switch (weekday) {
      case DateTime.monday:
        return 'Mon';
      case DateTime.tuesday:
        return 'Tue';
      case DateTime.wednesday:
        return 'Wed';
      case DateTime.thursday:
        return 'Thu';
      case DateTime.friday:
        return 'Fri';
      case DateTime.saturday:
        return 'Sat';
      case DateTime.sunday:
        return 'Sun';
      default:
        return '';
    }
  }
}
