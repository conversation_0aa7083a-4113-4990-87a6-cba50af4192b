import 'package:flutter/material.dart';
import 'package:commitime_test/models/api_model/report_data_model.dart';

class ReportTile extends StatelessWidget {
  final ReportData reportData;
  const ReportTile({super.key, required this.reportData});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.grey.shade300,
        borderRadius: BorderRadius.circular(20),
      ),
      child: ListTile(
        title: Text(
          "The Total Screen Time is ${reportData.screenTime} minutes",
          style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w700),
        ),
        subtitle: Text(
          reportData.reportData,
          style: const TextStyle(color: Color(0xff686B70), fontSize: 14),
        ),
        leading: CircleAvatar(
          radius: 30,
          backgroundColor: Colors.purple.shade300,
          child: const Text("R", style: TextStyle(color: Colors.white)),
        ),
      ),
    );
  }
}
