import 'dart:convert';

class AppUsageDetail {
  final String app;
  final int minutes;

  AppUsageDetail({
    required this.app,
    required this.minutes,
  });

  factory AppUsageDetail.fromJson(Map<String, dynamic> json) {
    return AppUsageDetail(
      app: json['app'] ?? '',
      minutes: json['minutes'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'app': app,
      'minutes': minutes,
    };
  }
}

class DailyReportModel {
  final String message;
  final DailyReportData data;

  DailyReportModel({
    required this.message,
    required this.data,
  });

  factory DailyReportModel.fromJson(Map<String, dynamic> json) {
    return DailyReportModel(
      message: json['message'] ?? '',
      data: DailyReportData.fromJson(json['data'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'data': data.toJson(),
    };
  }
}

class DailyReportData {
  final int userId;
  final String reportDate;
  final String screenTimeMinutes;
  final String appUsageDetails;
  final String updatedAt;
  final String createdAt;
  final int id;

  DailyReportData({
    required this.userId,
    required this.reportDate,
    required this.screenTimeMinutes,
    required this.appUsageDetails,
    required this.updatedAt,
    required this.createdAt,
    required this.id,
  });

  factory DailyReportData.fromJson(Map<String, dynamic> json) {
    return DailyReportData(
      userId: json['user_id'] ?? 0,
      reportDate: json['report_date'] ?? '',
      screenTimeMinutes: json['screen_time_minutes'] ?? '0',
      appUsageDetails: json['app_usage_details'] ?? '[]',
      updatedAt: json['updated_at'] ?? '',
      createdAt: json['created_at'] ?? '',
      id: json['id'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'report_date': reportDate,
      'screen_time_minutes': screenTimeMinutes,
      'app_usage_details': appUsageDetails,
      'updated_at': updatedAt,
      'created_at': createdAt,
      'id': id,
    };
  }

  // Helper method to parse app usage details from JSON string
  List<AppUsageDetail> getParsedAppUsageDetails() {
    try {
      final List<dynamic> jsonList = appUsageDetails.isNotEmpty
          ? (appUsageDetails.startsWith('[')
              ? json.decode(appUsageDetails)
              : [])
          : [];

      return jsonList
          .map((item) => AppUsageDetail.fromJson(item as Map<String, dynamic>))
          .toList();
    } catch (e) {
      return [];
    }
  }
}
