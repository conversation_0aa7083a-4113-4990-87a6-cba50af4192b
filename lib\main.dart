import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:commitime_test/cubits/get_report_data/get_report_data_cubit.dart';
import 'package:commitime_test/cubits/get_user_data/get_user_data_cubit.dart';

import 'package:commitime_test/screens/logo.dart';
import 'package:commitime_test/screens/chat_ai.dart';
import 'package:commitime_test/screens/customengav.dart';

import 'package:commitime_test/screens/permissions_screen.dart';
import 'package:commitime_test/service/app_usage_service.dart';
import 'package:commitime_test/service/automatic_daily_report_service.dart';
import 'package:commitime_test/utils/shared_prefs.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize automatic daily report service
  await AutomaticDailyReportService.initialize();

  // Set up method channel for native communication
  final methodChannel =
      const MethodChannel('com.example.commitime_test/methods');
  methodChannel.setMethodCallHandler((call) async {
    if (call.method == 'saveChatParameters') {
      try {
        final Map<dynamic, dynamic> args = call.arguments;
        final String appName = args['app_name'] as String;
        final String usageTime = args['usage_time'] as String;

        // Save the parameters to SharedPrefs
        final result = await SharedPrefs.saveChatParameters(appName, usageTime);
        return result;
      } catch (e) {
        throw PlatformException(
          code: 'SAVE_FAILED',
          message: 'Failed to save chat parameters: $e',
        );
      }
    }
    return null;
  });

  // Now run the app
  runApp(
    MultiBlocProvider(
      providers: [
        BlocProvider<GetUserDataCubit>(
          create: (_) => GetUserDataCubit(),
        ),
        BlocProvider<GetReportDataCubit>(
            create: (_) => GetReportDataCubit(dio: Dio()))
      ],
      child: MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: AppStartupHandler(),
      routes: {
        '/chat_ai': (context) => const ChatAiScreen(),
        '/custome_nav': (context) => FutureBuilder<String?>(
              future: SharedPrefs.getUserToken(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Scaffold(
                    body: Center(
                      child:
                          CircularProgressIndicator(color: Color(0xffBF65EB)),
                    ),
                  );
                }
                final token = snapshot.data ?? '';
                return CustomeNagv(token: token);
              },
            ),
      },
    );
  }
}

class AppStartupHandler extends StatefulWidget {
  const AppStartupHandler({super.key});

  @override
  State<AppStartupHandler> createState() => _AppStartupHandlerState();
}

class _AppStartupHandlerState extends State<AppStartupHandler> {
  bool _checkedPermissions = false;
  bool _hasPermissions = false;
  bool _isLoading = true;
  bool _isLoggedIn = false;
  String? _userToken;

  @override
  void initState() {
    super.initState();
    _checkLoginStatusAndPermissions();
  }

  Future<void> _checkLoginStatusAndPermissions() async {
    // Check if user is logged in
    _isLoggedIn = await SharedPrefs.isLoggedIn();
    _userToken = await SharedPrefs.getUserToken();

    // Load user data if logged in
    if (_isLoggedIn && _userToken != null) {
      // Get user data from SharedPrefs
      final userName = await SharedPrefs.getUserName();
      final userEmail = await SharedPrefs.getUserEmail();

      // Update the UserDataCubit if we have the data and widget is still mounted
      if (userName != null && userEmail != null && mounted) {
        context.read<GetUserDataCubit>().getUserData(
              name: userName,
              email: userEmail,
            );
      }
    }

    // Check if we have the required permissions
    final hasUsageStats = await AppUsageService.hasUsageStatsPermission();
    final hasOverlay = await AppUsageService.hasOverlayPermission();

    setState(() {
      _hasPermissions = hasUsageStats && hasOverlay;
      _checkedPermissions = true;
      _isLoading = false;
    });

    // If we have permissions, start the monitoring service
    if (_hasPermissions) {
      await AppUsageService.startMonitoringService();

      // Reschedule automatic daily reports if user is logged in
      if (_isLoggedIn && _userToken != null) {
        await AutomaticDailyReportService.reschedule();
      }
    }
  }

  void _onPermissionsGranted() {
    setState(() {
      _hasPermissions = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(color: Color(0xffBF65EB)),
        ),
      );
    }

    // If permissions are not granted, show the permissions screen
    if (_checkedPermissions && !_hasPermissions) {
      return PermissionsScreen(onPermissionsGranted: _onPermissionsGranted);
    }

    // If user is logged in and has a token, go directly to CustomeNagv
    if (_isLoggedIn && _userToken != null) {
      return CustomeNagv(token: _userToken!);
    }

    // Otherwise, start with the normal flow
    return Logo();
  }
}
