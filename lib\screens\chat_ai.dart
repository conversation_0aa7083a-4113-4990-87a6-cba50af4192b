import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:commitime_test/service/ai_chat_service.dart';

import 'package:commitime_test/utils/shared_prefs.dart';
import 'dart:developer' as developer;

class ChatAiScreen extends StatefulWidget {
  final String? appName;
  final String? usageDuration;

  const ChatAiScreen({
    super.key,
    this.appName,
    this.usageDuration,
  });

  @override
  State<ChatAiScreen> createState() => _ChatAiScreenState();
}

class _ChatAiScreenState extends State<ChatAiScreen>
    with WidgetsBindingObserver {
  final TextEditingController _messageController = TextEditingController();
  final List<ChatMessage> _messages = [];
  final ScrollController _scrollController = ScrollController();
  final AiChatService _aiChatService = AiChatService(dio: Dio());
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    _loadChatData();
  }

  Future<void> _loadChatData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // First check if we have direct parameters from the widget
      if (widget.appName != null &&
          widget.usageDuration != null &&
          widget.appName!.isNotEmpty &&
          widget.usageDuration!.isNotEmpty) {
        developer.log(
            "Using widget parameters: appName=${widget.appName}, usageDuration=${widget.usageDuration}",
            name: "ChatAiScreen");

        _fetchGeneratedQuestion(widget.appName!, widget.usageDuration!);
        return;
      }

      // If not, try to get them from shared preferences
      final appName = await SharedPrefs.getChatAppName();
      final usageTime = await SharedPrefs.getChatUsageTime();

      developer.log(
          "Retrieved from SharedPrefs: appName=$appName, usageTime=$usageTime",
          name: "ChatAiScreen");

      if (appName != null &&
          usageTime != null &&
          appName.isNotEmpty &&
          usageTime.isNotEmpty &&
          mounted) {
        _fetchGeneratedQuestion(appName, usageTime);
      } else {
        // If we still don't have the data, use default values
        developer.log("No parameters found, using default values",
            name: "ChatAiScreen");

        if (mounted) {
          // Use default values for testing
          _fetchGeneratedQuestion("TikTok", "2 hours");
        }
      }
    } catch (e) {
      developer.log("Error in _loadChatData: $e", name: "ChatAiScreen");

      if (mounted) {
        setState(() {
          _isLoading = false;
          _messages.add(
            ChatMessage(
              text: "Welcome to Chat AI. How can I help you today?",
              isUser: false,
            ),
          );
        });
        _scrollToBottom();
      }
    }
  }

  // Ensure the token is available before making API calls
  Future<void> _ensureToken() async {
    // Check if we have a token
    final token = await SharedPrefs.getUserToken();
    if (token == null || token.isEmpty) {
      developer.log("No token found, using default token",
          name: "ChatAiScreen");

      // For testing purposes, use a default token
      const defaultToken =
          "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2FjMjctMTk3LTM1LTc2LTE1My5uZ3Jvay1mcmVlLmFwcC9hcGkvYXV0aC9sb2dpbiIsImlhdCI6MTcxNzQyMzA1OCwiZXhwIjoxNzE3NDI2NjU4LCJuYmYiOjE3MTc0MjMwNTgsImp0aSI6IkRJRGJJRGJJRGJJRGJJRGJJRGJJRGIiLCJzdWIiOiIxIiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.YOUR_SIGNATURE_HERE";
      await SharedPrefs.setUserToken(defaultToken);
      developer.log("Default token saved", name: "ChatAiScreen");
    } else {
      developer.log("Token found: ${token.substring(0, 10)}...",
          name: "ChatAiScreen");
    }
  }

  Future<void> _fetchGeneratedQuestion(String appName, String usageTime) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Ensure we have a token before making the API call
      await _ensureToken();

      developer.log("Calling API with appName=$appName, usageTime=$usageTime",
          name: "ChatAiScreen");

      final response = await _aiChatService.getGenerateQuestion(
        appName: appName,
        usageTime: usageTime,
      );

      developer.log("API response received: ${response.question}",
          name: "ChatAiScreen");

      if (mounted) {
        setState(() {
          _messages.add(
            ChatMessage(
              text: response.question,
              isUser: false,
            ),
          );
          _isLoading = false;
        });
        _scrollToBottom();
      }
    } catch (e) {
      developer.log("Error calling API: $e", name: "ChatAiScreen");

      if (mounted) {
        setState(() {
          _messages.add(
            ChatMessage(
              text: "Sorry, I couldn't generate a question. Please try again.",
              isUser: false,
            ),
          );
          _isLoading = false;
        });
        _scrollToBottom();
      }
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    // Scroll to bottom when keyboard appears
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  Future<void> _handleSubmitted(String text) async {
    if (text.trim().isEmpty) return;

    // Clear the input field
    _messageController.clear();

    // Add user message to chat
    setState(() {
      _messages.add(
        ChatMessage(
          text: text,
          isUser: true,
        ),
      );
      // Show loading indicator
      _isLoading = true;
    });
    _scrollToBottom();

    try {
      // Step 1: Ensure we have a token
      await _ensureToken();

      // Step 2: Analyze the emotion in the user's message
      developer.log("Analyzing emotion for: $text", name: "ChatAiScreen");

      // Show typing indicator
      if (mounted) {
        setState(() {
          _messages.add(
            ChatMessage(
              text: "Analyzing your response...",
              isUser: false,
              isTyping: true,
            ),
          );
        });
        _scrollToBottom();
      }

      final emotionResponse = await _aiChatService.getAnalyzeEmotion(
        userResponse: text,
      );

      developer.log(
          "Emotion analysis result: category=${emotionResponse.category}, emotion=${emotionResponse.emotion}",
          name: "ChatAiScreen");

      // Remove typing indicator
      if (mounted) {
        setState(() {
          _messages.removeWhere((message) => message.isTyping == true);
        });
      }

      // Step 3: Get a suggestion based on the emotion
      developer.log(
          "Getting suggestion for emotion: ${emotionResponse.emotion}",
          name: "ChatAiScreen");

      // Show typing indicator again
      if (mounted) {
        setState(() {
          _messages.add(
            ChatMessage(
              text: "Thinking of a suggestion...",
              isUser: false,
              isTyping: true,
            ),
          );
        });
        _scrollToBottom();
      }

      try {
        final suggestionResponse = await _aiChatService.getSuggestion(
          emtion: emotionResponse.emotion,
        );

        developer.log("Suggestion result: ${suggestionResponse.suggestion}",
            name: "ChatAiScreen");

        // Remove typing indicator
        if (mounted) {
          setState(() {
            _messages.removeWhere((message) => message.isTyping == true);
          });
        }

        // Step 4: Display the suggestion in the chat
        if (mounted) {
          setState(() {
            _messages.add(
              ChatMessage(
                text: suggestionResponse.suggestion,
                isUser: false,
              ),
            );
            _isLoading = false;
          });
          _scrollToBottom();
        }
      } catch (e) {
        developer.log("Error getting suggestion: $e", name: "ChatAiScreen");

        // Remove typing indicator
        if (mounted) {
          setState(() {
            _messages.removeWhere((message) => message.isTyping == true);
          });
        }

        // Display a fallback message
        if (mounted) {
          setState(() {
            _messages.add(
              ChatMessage(
                text:
                    "Based on your response, I'd suggest taking a moment to reflect on what activities align with your values and goals right now.",
                isUser: false,
              ),
            );
            _isLoading = false;
          });
          _scrollToBottom();
        }
      }

      // This section is now handled in the try-catch block above
    } catch (e) {
      developer.log("Error in message flow: $e", name: "ChatAiScreen");

      // Remove any typing indicators
      if (mounted) {
        setState(() {
          _messages.removeWhere((message) => message.isTyping == true);

          // Add error message
          _messages.add(
            ChatMessage(
              text:
                  "I'm having trouble processing your message. Please try again later.",
              isUser: false,
            ),
          );
          _isLoading = false;
        });
        _scrollToBottom();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leadingWidth: 20,
        leading: IconButton(
          onPressed: () {
            // Navigate to CustomeNagv screen and remove all previous routes
            Navigator.pushNamedAndRemoveUntil(
              context,
              '/custome_nav',
              (route) => false,
            );
          },
          icon: const Icon(Icons.arrow_back, color: Colors.white),
        ),
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircleAvatar(
              radius: 18,
              backgroundColor: Colors.white,
              child: Image.asset(
                "assets/images/aichat.png",
                scale: 6,
              ),
            ),
            const Text(
              '  Chat AI',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
        backgroundColor: const Color(0xffBF65EB),
      ),
      body: Column(
        children: [
          Expanded(
            child: _messages.isEmpty && _isLoading
                ? const Center(
                    child: CircularProgressIndicator(
                      color: Color(0xffBF65EB),
                    ),
                  )
                : ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    itemCount: _messages.length,
                    itemBuilder: (context, index) => _messages[index],
                  ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(25),
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey,
                          spreadRadius: 1,
                          blurRadius: 3,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: TextField(
                      controller: _messageController,
                      decoration: const InputDecoration(
                        hintText: 'Type a message',
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(horizontal: 16.0),
                      ),
                      onSubmitted: _handleSubmitted,
                      enabled: !_isLoading, // Disable input while processing
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey,
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.send, color: Color(0xffBF65EB)),
                    onPressed: _isLoading
                        ? null
                        : () => _handleSubmitted(_messageController.text),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class ChatMessage extends StatelessWidget {
  final String text;
  final bool isUser;
  final bool isTyping;

  const ChatMessage({
    super.key,
    required this.text,
    required this.isUser,
    this.isTyping = false,
  });

  @override
  Widget build(BuildContext context) {
    // For typing indicator, show a different UI
    if (isTyping) {
      return Container(
        margin: const EdgeInsets.symmetric(vertical: 10.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.7,
              ),
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey,
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Color(0xffBF65EB),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Text(
                    text,
                    style: const TextStyle(fontSize: 16.0),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    // Regular message bubble
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10.0),
      child: Row(
        mainAxisAlignment:
            isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.7,
            ),
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
            decoration: BoxDecoration(
              color: isUser ? const Color(0xffe2a1ed) : Colors.white,
              borderRadius: BorderRadius.circular(20.0),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey,
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Text(
              text,
              style: const TextStyle(fontSize: 16.0),
            ),
          ),
        ],
      ),
    );
  }
}
