import 'package:flutter/material.dart';
import 'package:animated_splash_screen/animated_splash_screen.dart';
import 'package:page_transition/page_transition.dart';
import 'package:commitime_test/screens/splash.dart';

class Logo extends StatelessWidget {
  const Logo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Center(
        child: AnimatedSplashScreen(
          backgroundColor: Colors.black,
          pageTransitionType: PageTransitionType.fade,
          animationDuration: const Duration(seconds: 1),
          splashTransition: SplashTransition.rotationTransition,
          splash: Image.asset("assets/images/Logo.png"),
          nextScreen: const Splash(),
        ),
      ),
    );
  }
}
