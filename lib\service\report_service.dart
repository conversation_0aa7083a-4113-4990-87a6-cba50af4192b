import 'package:dio/dio.dart';
import 'package:commitime_test/models/api_model/report_data_model.dart';

/*final dio = Dio(BaseOptions(
  headers: {
    'Authorization':'Bearer ${token}',
    'Accept': 'application/json',
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  },
  followRedirects: false,
  validateStatus: (status) => status != null && status < 500,
));*/
class ReportService {
  final Dio dio;
  final String baseUrl = "https://1984-197-35-7-0.ngrok-free.app/api/";
  final String endPoient = "reports";
  ReportService({required this.dio});
  Future<List<ReportData>> getReport() async {
    try {
      Response response = await dio.get("$baseUrl$endPoient");
      if (response.statusCode == 200) {
        Map<String, dynamic> jsonData = response.data;
        List<dynamic> data = jsonData['data'];
        List<ReportData> reportData = [];
        for (var item in data) {
          reportData.add(ReportData.fromJson(item));
        }
        return reportData;
      }
    } on DioException catch (e) {
      final String errMessage = e.response?.data?['message'] ?? "try later";
      throw Exception(errMessage);
    }
    return [];
  }
}
