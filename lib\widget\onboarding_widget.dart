//import 'package:simple_gradient_text/simple_gradient_text.dart';

import 'package:flutter/material.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:commitime_test/models/onboarding_item.dart';
import 'package:commitime_test/screens/welcome.dart';
import 'package:commitime_test/widget/onboarding.dart';

class OnBoardingScreen extends StatefulWidget {
  const OnBoardingScreen({super.key});

  @override
  State<OnBoardingScreen> createState() => _OnBoardingScreenState();
}

class _OnBoardingScreenState extends State<OnBoardingScreen> {
  final controller = OnBooardingItem();
  final pagecontroller = PageController();
  bool isLastPage = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      bottomSheet: Container(
        color: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
        child: isLastPage
            ? getStart()
            : Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ElevatedButton(
                      style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white),
                      onPressed: () => pagecontroller
                          .jumpToPage(controller.onboarding.length - 1),
                      child: Text(
                        "Skip",
                        style: TextStyle(
                          color: const Color(0xffBF65EB),
                        ),
                      )),
                  SmoothPageIndicator(
                    controller: pagecontroller,
                    count: controller.onboarding.length,
                    effect: const WormEffect(
                        activeDotColor: Color(0xffe2a1ed),
                        dotHeight: 12,
                        dotWidth: 12),
                    onDotClicked: (index) => pagecontroller.animateToPage(index,
                        duration: const Duration(milliseconds: 600),
                        curve: Curves.easeIn),
                  ),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                        backgroundColor: Color(0xffBF65EB),
                        shape: CircleBorder(
                            side: BorderSide(color: Color(0xffBF65EB)))),
                    onPressed: () => pagecontroller.nextPage(
                        duration: const Duration(milliseconds: 600),
                        curve: Curves.easeIn),
                    child: Icon(Icons.arrow_forward_ios, color: Colors.white),

                    /*GradientText(
                        "Next",
                        colors: const [Color(0xfffdcec3), Color(0xffe2a1ed)],
                        gradientType: GradientType.linear,
                        gradientDirection: GradientDirection.ltr,
                      )*/
                  )
                ],
              ),
      ),
      body: PageView.builder(
        onPageChanged: (value) => setState(
            () => isLastPage = controller.onboarding.length - 1 == value),
        controller: pagecontroller,
        itemCount: controller.onboarding.length,
        itemBuilder: (context, index) {
          return OnBoardingWidget(onBoarding: controller.onboarding[index]);
        },
      ),
    );
  }

  Widget getStart() {
    return Container(
      height: 45,
      width: double.infinity,
      decoration: BoxDecoration(
          color: const Color(0xffBF65EB),
          borderRadius: BorderRadius.circular(15)),
      child: TextButton(
          onPressed: () {
            Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const Welcome(),
                ));
          },
          child: const Text(
            "Get Start",
            style: TextStyle(color: Colors.white, fontSize: 18),
          )),
    );
  }
}
