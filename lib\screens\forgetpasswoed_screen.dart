/*
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:test/models/forgetpassword_item.dart';
import 'package:test/widget/forgetpassword_widget.dart';

class ScForgetPass extends StatelessWidget {
  ScForgetPass({super.key});
  final controller = ForgetpasswordItem();
  final PageController pagecontroller = PageController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: PageView.builder(
        controller: pagecontroller,
        itemCount: controller.forgetpass.length,
        itemBuilder: (context, index) {
          log("$index");
          return ForgetpasswordWidget(
            forgetpass: controller.forgetpass[index],
            pageController: pagecontroller,
            index: index,
            totalPage: controller.forgetpass.length,
          );
        },
      ),
    );
  }
}
*/
