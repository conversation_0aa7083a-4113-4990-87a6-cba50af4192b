import 'package:commitime_test/widget/streak.dart';
import 'package:flutter/material.dart';
import 'package:commitime_test/service/app_usage_service.dart';
import 'package:commitime_test/widget/apps_list_tile.dart';

class SetTimeLimit extends StatefulWidget {
  const SetTimeLimit({super.key});

  @override
  State<SetTimeLimit> createState() => _SetTimeLimitState();
}

class _SetTimeLimitState extends State<SetTimeLimit> {
  List<AppUsageData> _usageData = [];
  Map<String, int> _appLimits = {};
  Map<String, bool> _exceededLimits =
      {}; // Track which apps have exceeded their limits
  bool _isLoading = true;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  // Filter states
  String _selectedFilter = 'all'; // 'all', 'limited', 'blocked'

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final usageData =
          await AppUsageService.getUsageStats(1); // Get today's data
      final appLimits = await AppUsageService.getAppLimits();

      // Calculate which apps have exceeded their limits
      Map<String, bool> exceededLimits = {};
      for (var app in usageData) {
        final limit = appLimits[app.packageName];
        if (limit != null && limit > 0) {
          // Convert usage time to minutes and compare with limit
          final usageMinutes = app.timeInForegroundMs / (1000 * 60);
          exceededLimits[app.packageName] = usageMinutes >= limit;
        }
      }

      setState(() {
        _usageData = usageData;
        _appLimits = appLimits;
        _exceededLimits = exceededLimits;
        _isLoading = false;
      });
    } catch (e) {
      print("Error loading data: $e");
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<AppUsageData> get _filteredApps {
    List<AppUsageData> filteredBySearch = _searchQuery.isEmpty
        ? _usageData
        : _usageData
            .where((app) =>
                app.appName.toLowerCase().contains(_searchQuery.toLowerCase()))
            .toList();

    // Apply category filter
    switch (_selectedFilter) {
      case 'limited':
        return filteredBySearch
            .where((app) =>
                _appLimits.containsKey(app.packageName) &&
                _appLimits[app.packageName]! > 0)
            .toList();
      case 'blocked':
        return filteredBySearch
            .where((app) => _exceededLimits[app.packageName] == true)
            .toList();
      default:
        return filteredBySearch;
    }
  }

  Widget _buildFilterChips() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0, vertical: 8.0),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            _buildFilterChip('All Apps', 'all'),
            const SizedBox(width: 8),
            _buildFilterChip('Limited Apps', 'limited'),
            const SizedBox(width: 8),
            _buildFilterChip('Blocked Apps', 'blocked'),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    final isSelected = _selectedFilter == value;
    return FilterChip(
      label: Text(
        label,
        style: TextStyle(
          color: isSelected ? Colors.white : Colors.grey,
          fontWeight: FontWeight.w500,
        ),
      ),
      selected: isSelected,
      onSelected: (bool selected) {
        setState(() {
          _selectedFilter = value;
        });
      },
      backgroundColor: Colors.white,
      selectedColor: const Color(0xffBF65EB),
      checkmarkColor: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(
          color: isSelected ? const Color(0xffBF65EB) : Colors.grey,
          width: 1,
        ),
      ),
    );
  }

  Future<void> _showSetLimitDialog(String packageName, String appName) async {
    final currentLimit = _appLimits[packageName] ?? 0;

    // Calculate initial hours and minutes from the current limit
    final int initialHours = currentLimit ~/ 60;
    final int initialMinutes =
        (currentLimit % 60) ~/ 5 * 5; // Round to nearest 5

    // Create scroll controllers with initial positions
    final FixedExtentScrollController hoursController =
        FixedExtentScrollController(
      initialItem: initialHours,
    );

    final FixedExtentScrollController minutesController =
        FixedExtentScrollController(
      initialItem: initialMinutes ~/ 5, // Convert minutes to index (0-11)
    );

    // Track selected values
    int selectedHours = initialHours;
    int selectedMinutes = initialMinutes;

    // We'll dispose the controllers when the dialog is closed
    final result = await showDialog<int>(
      context: context,
      barrierDismissible: true,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: Text('Set Time Limit for $appName'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                    'Set a daily time limit for this app. When the limit is reached, the app will be blocked.'
                    '${currentLimit > 0 ? '\nCurrent limit: ${_formatTimeLimit(currentLimit)}' : ''}'),
                const SizedBox(height: 16),
                Container(
                  height: 200,
                  decoration: BoxDecoration(
                    color: const Color(0xFFF7E5FF), // Light purple background
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Hours wheel
                      Expanded(
                        child: Container(
                          margin: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: Colors.grey
                                .shade200, // Light grey background for wheel
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: ListWheelScrollView.useDelegate(
                            controller: hoursController,
                            itemExtent: 60, // Larger item height
                            diameterRatio: 1.7, // More curved wheel
                            physics: const BouncingScrollPhysics(),

                            onSelectedItemChanged: (index) {
                              setState(() {
                                selectedHours = index % 24;
                              });
                            },
                            childDelegate: ListWheelChildLoopingListDelegate(
                              children: List<Widget>.generate(
                                24,
                                (index) => Container(
                                  alignment: Alignment.center,
                                  child: Text(
                                    index.toString().padLeft(
                                        2, '0'), // Pad with leading zero
                                    style: TextStyle(
                                      fontSize: 32,
                                      fontWeight: FontWeight.bold,
                                      color: Color.fromRGBO(
                                        191, 101,
                                        235, // RGB values for 0xffBF65EB
                                        selectedHours == index ? 1.0 : 0.5,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),

                      // Colon separator
                      Container(
                        alignment: Alignment.center,
                        child: const Text(
                          ":",
                          style: TextStyle(
                            fontSize: 36,
                            fontWeight: FontWeight.bold,
                            color: Color(0xffBF65EB),
                          ),
                        ),
                      ),

                      // Minutes wheel
                      Expanded(
                        child: Container(
                          margin: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: Colors.grey
                                .shade200, // Light grey background for wheel
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: ListWheelScrollView.useDelegate(
                            controller: minutesController,
                            itemExtent: 60, // Larger item height
                            diameterRatio: 1.7, // More curved wheel
                            physics: const BouncingScrollPhysics(),

                            onSelectedItemChanged: (index) {
                              setState(() {
                                selectedMinutes = (index % 12) * 5;
                              });
                            },
                            childDelegate: ListWheelChildLoopingListDelegate(
                              children: List<Widget>.generate(
                                12, // 0, 5, 10, ..., 55
                                (index) {
                                  final minutes = index * 5;
                                  return Container(
                                    alignment: Alignment.center,
                                    child: Text(
                                      minutes.toString().padLeft(
                                          2, '0'), // Pad with leading zero
                                      style: TextStyle(
                                        fontSize: 32,
                                        fontWeight: FontWeight.bold,
                                        color: Color.fromRGBO(
                                          191, 101,
                                          235, // RGB values for 0xffBF65EB
                                          selectedMinutes == minutes
                                              ? 1.0
                                              : 0.5,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, 0),
                child: const Text('Remove Limit'),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Cancel'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      // Convert hours and minutes to total minutes
                      final value = (selectedHours * 60) + selectedMinutes;
                      Navigator.pop(context, value);
                    },
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                          side: BorderSide(color: const Color(0xffBF65EB))),
                      backgroundColor: const Color(0xffBF65EB),
                    ),
                    child: const Text(
                      'Set Limit',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );

    // Dispose controllers after dialog is closed
    hoursController.dispose();
    minutesController.dispose();

    if (result != null) {
      final success = await AppUsageService.setAppLimit(packageName, result);

      if (success) {
        setState(() {
          if (result > 0) {
            _appLimits[packageName] = result;
          } else {
            _appLimits.remove(packageName);
          }
        });
      }
    }
  }

  // Format time limit in hours and minutes
  String _formatTimeLimit(int minutes) {
    final int hours = minutes ~/ 60;
    final int remainingMinutes = minutes % 60;

    if (hours > 0) {
      return remainingMinutes > 0
          ? "${hours}h ${remainingMinutes}m"
          : "${hours}h";
    } else {
      return "${minutes}m";
    }
  }

  String _getAppIconPath(String packageName) {
    if (packageName.contains('facebook')) {
      return "assets/images/Facebook.png";
    } else if (packageName.contains('instagram')) {
      return "assets/images/Group 14.png";
    } else if (packageName.contains('youtube')) {
      return "assets/images/youtube.png";
    } else if (packageName.contains('whatsapp')) {
      return "assets/images/whatsapp.png";
    } else if (packageName.contains('music') ||
        packageName.contains('spotify')) {
      return "assets/images/Music.png";
    } else {
      return "assets/images/Logo.png";
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        scrolledUnderElevation: 0,
        backgroundColor: Colors.white,
        centerTitle: true,
        title: const Text(
          "Set Time Limits",
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
        ),
        actions: [Streak(), const SizedBox(width: 16)],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Color(0xffBF65EB)))
          : RefreshIndicator(
              onRefresh: _loadData,
              color: const Color(0xffBF65EB),
              child: ListView(
                children: [
                  // Search bar
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Search apps...',
                        hintStyle: TextStyle(color: Colors.grey),
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                      ),
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value;
                        });
                      },
                    ),
                  ),

                  // Filter chips
                  _buildFilterChips(),

                  // Stats summary
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatCard(
                          'Total Apps',
                          _usageData.length.toString(),
                          Icons.apps,
                        ),
                        _buildStatCard(
                          'Limited',
                          _appLimits.length.toString(),
                          Icons.timer,
                        ),
                        _buildStatCard(
                          'Blocked',
                          _exceededLimits.values
                              .where((exceeded) => exceeded)
                              .length
                              .toString(),
                          Icons.block,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // App list
                  if (_filteredApps.isEmpty)
                    const Center(
                      child: Padding(
                        padding: EdgeInsets.all(32.0),
                        child: Text(
                          "No apps found",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                    )
                  else
                    Column(
                      children: _filteredApps.map((app) {
                        double totalMs = 0;
                        for (var appData in _usageData) {
                          totalMs += appData.timeInForegroundMs;
                        }
                        final rawPercentage = totalMs > 0
                            ? app.timeInForegroundMs / totalMs
                            : 0.0;
                        final formattedPercentageString =
                            (rawPercentage * 100).toStringAsFixed(1);
                        final sliderValue = rawPercentage.clamp(0.0, 1.0);
                        final currentLimit = _appLimits[app.packageName] ?? 0;
                        // Format time limit in hours and minutes
                        final String? limitText = currentLimit > 0
                            ? "Limit: ${_formatTimeLimit(currentLimit)}"
                            : null;

                        return GestureDetector(
                          onTap: () =>
                              _showSetLimitDialog(app.packageName, app.appName),
                          child: AppsListTile(
                            pathImage: _getAppIconPath(app.packageName),
                            title: app.appName,
                            slidervalue: sliderValue,
                            time: app.formattedDuration,
                            percentageText: formattedPercentageString,
                            limitText: limitText,
                            color: const Color(0xffE9ECF1),
                            iconBase64: app.iconBase64,
                          ),
                        );
                      }).toList(),
                    ),
                ],
              ),
            ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: const Color(0xffF7E5FF),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xffBF65EB), width: 1),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: const Color(0xffBF65EB), size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xffBF65EB),
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xffBF65EB),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
