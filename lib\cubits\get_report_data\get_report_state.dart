import 'package:commitime_test/models/api_model/report_data_model.dart';

class GetReportState {}

class GetReportInitial extends GetReportState {}

class GetReportLoading extends GetReportState {}

class GetReportSuccess extends GetReportState {
  final List<ReportData> reportData;
  final String token;

  GetReportSuccess({required this.reportData, required this.token});
}

class GetReportError extends GetReportState {
  final String errorMessage; // <-- يمكنك تمرير رسالة الخطأ
  GetReportError({required this.errorMessage});
}

class GetReportEmpty extends GetReportState {}
