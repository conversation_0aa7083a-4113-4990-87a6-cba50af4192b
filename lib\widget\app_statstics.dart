import 'package:flutter/material.dart';

import 'package:commitime_test/widget/info_statistics.dart';
import 'package:commitime_test/widget/streak.dart';
import 'package:commitime_test/widget/usage_chart.dart';

class AppStatistics extends StatelessWidget {
  const AppStatistics({super.key, required this.title, required this.icon});
  final String title;
  final IconData icon;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        scrolledUnderElevation: 0,
        backgroundColor: Colors.white,
        leading: IconButton(
          onPressed: () {
            Navigator.pop(context);
          },
          icon: const Icon(Icons.arrow_back_ios, color: Color(0xffEC4CFF)),
        ),
        centerTitle: true,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              "assets/images/Facebook.png",
              height: 35,
            ),
            Text(
              " FaceBook",
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
            ),
          ],
        ),
        actions: [Streak(), const SizedBox(width: 16)],
      ),
      body: ListView(
        scrollDirection: Axis.vertical,
        children: [
          InfoStatistics(),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Divider(
              thickness: 2,
              height: 0,
              color: const Color(0xffBF65EB),
            ),
          ),
          const SizedBox(height: 8),
          const Center(
            child: Text(
              "11hrs, 20mins, 33swc screen time",
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(24.0),
            child: SizedBox(height: 500, width: 315, child: UsageChart()),
          ),
        ],
      ),
    );
  }
}
