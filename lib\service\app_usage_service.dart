import 'package:flutter/services.dart';

class AppUsageService {
  static const platform =
      MethodChannel('com.example.app_usage_tracker/channel');

  // Check if the app has usage stats permission
  static Future<bool> hasUsageStatsPermission() async {
    try {
      final bool? granted =
          await platform.invokeMethod('checkUsageStatsPermission');
      return granted ?? false;
    } on PlatformException catch (e) {
      print("Failed to check usage stats permission: '${e.message}'.");
      return false;
    }
  }

  // Request usage stats permission
  static Future<void> requestUsageStatsPermission() async {
    try {
      await platform.invokeMethod('requestUsageStatsPermission');
    } on PlatformException catch (e) {
      print("Failed to request usage stats permission: '${e.message}'.");
    }
  }

  // Check if the app has overlay permission
  static Future<bool> hasOverlayPermission() async {
    try {
      final bool? granted =
          await platform.invokeMethod('checkOverlayPermission');
      return granted ?? false;
    } on PlatformException catch (e) {
      print("Failed to check overlay permission: '${e.message}'.");
      return false;
    }
  }

  // Request overlay permission
  static Future<void> requestOverlayPermission() async {
    try {
      await platform.invokeMethod('requestOverlayPermission');
    } on PlatformException catch (e) {
      print("Failed to request overlay permission: '${e.message}'.");
    }
  }

  // Start the monitoring service
  static Future<bool> startMonitoringService() async {
    try {
      final bool? started =
          await platform.invokeMethod('startMonitoringService');
      return started ?? false;
    } on PlatformException catch (e) {
      print("Failed to start monitoring service: '${e.message}'.");
      return false;
    }
  }

  // Get installed apps
  static Future<List<Map<String, String?>>> getInstalledApps() async {
    try {
      final List<dynamic>? appsResult =
          await platform.invokeMethod('getInstalledApps');
      if (appsResult == null) return [];

      return appsResult.map((app) {
        final Map<dynamic, dynamic> appMap = app as Map<dynamic, dynamic>;
        return {
          'appName': appMap['appName'] as String,
          'packageName': appMap['packageName'] as String,
          'iconBase64': appMap['iconBase64'] as String?,
        };
      }).toList();
    } on PlatformException catch (e) {
      print("Failed to get installed apps: '${e.message}'.");
      return [];
    }
  }

  // Set app limit
  static Future<bool> setAppLimit(String packageName, int limitMinutes) async {
    try {
      await platform.invokeMethod('setAppLimit', {
        'packageName': packageName,
        'limitMinutes': limitMinutes,
      });

      // Tell the service to reload the app limits
      await platform.invokeMethod('reloadAppLimits');
      return true;
    } on PlatformException catch (e) {
      print("Failed to set app limit: '${e.message}'.");
      return false;
    }
  }

  // Get app limits
  static Future<Map<String, int>> getAppLimits() async {
    try {
      final Map<dynamic, dynamic>? limitsResult =
          await platform.invokeMethod('getAppLimits');

      if (limitsResult == null) return {};

      // Convert to Map<String, int>
      return limitsResult
          .map((key, value) => MapEntry(key.toString(), value as int));
    } on PlatformException catch (e) {
      print("Failed to get app limits: '${e.message}'.");
      return {};
    }
  }

  // Get usage stats
  static Future<List<AppUsageData>> getUsageStats(int days) async {
    try {
      final Map<dynamic, dynamic>? result =
          await platform.invokeMethod('getUsageStats', {'days': days});

      if (result == null) return [];

      List<AppUsageData> usageList = [];

      result.forEach((key, value) {
        final stats = AppUsageData.fromMap(value as Map<dynamic, dynamic>);
        usageList.add(stats);
      });

      // Sort by usage time (descending)
      usageList
          .sort((a, b) => b.timeInForegroundMs.compareTo(a.timeInForegroundMs));

      // *** Add logging here to see the size and content of usageList ***
      print(
          "AppUsageService: Returning ${usageList.length} apps from getUsageStats.");
      // print(usageList.map((a) => a.packageName).toList()); // Optional: print package names

      return usageList;
    } on PlatformException catch (e) {
      print("Failed to get usage stats: '${e.message}'.");
      return [];
    }
  }
}

class AppUsageData {
  final String appName;
  final String packageName;
  final double timeInForegroundMs;
  final int timeInMinutes;
  final double lastTimeUsed;
  final String? iconBase64;

  AppUsageData({
    required this.appName,
    required this.packageName,
    required this.timeInForegroundMs,
    required this.lastTimeUsed,
    required this.timeInMinutes,
    this.iconBase64,
  });

  factory AppUsageData.fromMap(Map<dynamic, dynamic> map) {
    return AppUsageData(
      appName: map['appName'] as String,
      packageName: map['packageName'] as String,
      timeInForegroundMs: (map['timeInForegroundMs'] as int).toDouble(),
      timeInMinutes: map['timeInMinutes'] as int? ??
          (map['timeInForegroundMs'] as int) ~/ 60000,
      lastTimeUsed: (map['lastTimeUsed'] as int).toDouble(),
      iconBase64: map['iconBase64'] as String?,
    );
  }

  // Format the duration as a string (e.g., "2h 30m")
  String get formattedDuration {
    final hours = timeInMinutes ~/ 60;
    final minutes = timeInMinutes % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }
}
