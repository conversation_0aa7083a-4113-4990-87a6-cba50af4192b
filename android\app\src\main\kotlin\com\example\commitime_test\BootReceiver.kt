package com.example.commitime_test

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log

class BootReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "BootReceiver"
        private const val PREFS_NAME = "AppUsageTrackerPrefs"
        private const val PREF_SERVICE_ENABLED = "service_enabled"
    }

    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action == Intent.ACTION_BOOT_COMPLETED || 
            intent.action == "android.intent.action.QUICKBOOT_POWERON" ||
            intent.action == "com.htc.intent.action.QUICKBOOT_POWERON") {
            
            Log.d(TAG, "Boot completed, checking if service should be started.")
            
            // Check if service was enabled before reboot
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val serviceEnabled = prefs.getBoolean(PREF_SERVICE_ENABLED, false)
            
            if (serviceEnabled) {
                Log.d(TAG, "Service was enabled, scheduling delayed start.")
                // Delay start to avoid issues with system not being fully initialized
                Handler(Looper.getMainLooper()).postDelayed({
                    startMonitoringService(context)
                }, 60000) // 1 minute delay
            } else {
                Log.d(TAG, "Service was not enabled, not starting.")
            }
        }
    }
    
    private fun startMonitoringService(context: Context) {
        val serviceIntent = Intent(context, AppUsageMonitorService::class.java).apply {
            action = AppUsageMonitorService.ACTION_START_MONITORING
        }

        try {
            // Start foreground service appropriately based on Android version
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
                Log.i(TAG, "Foreground service start requested on boot.")
            } else {
                context.startService(serviceIntent)
                Log.i(TAG, "Service start requested on boot.")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start service on boot: ${e.message}", e)
        }
    }
} 