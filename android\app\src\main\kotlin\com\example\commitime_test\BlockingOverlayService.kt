package com.example.commitime_test

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.app.usage.UsageStatsManager
import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.graphics.PixelFormat
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.os.PowerManager
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import android.app.ActivityManager
import androidx.core.app.NotificationCompat

/**
 * This service creates an overlay that appears on top of EVERYTHING.
 * It completely blocks interaction with anything underneath.
 */
class BlockingOverlayService : Service() {

    companion object {
        private const val TAG = "BlockingOverlayService"
        const val EXTRA_APP_NAME = "extra_app_name"
        const val EXTRA_PACKAGE_NAME = "extra_package_name"
        const val EXTRA_LIMIT_MINUTES = "extra_limit_minutes"
        private const val CHECK_INTERVAL_MS = 100L
        const val CHANNEL_ID = "com.example.commitime_test.blocking"
        const val NOTIFICATION_ID = 2
    }

    private lateinit var windowManager: WindowManager
    private var overlayView: android.view.View? = null
    private var blockedAppPackage: String? = null
    private var blockedAppName: String? = null
    private var timeLimit: Int = 0
    private val handler = Handler(Looper.getMainLooper())
    private lateinit var checkRunnable: Runnable
    private var attemptCount = 0
    private var wakeLock: PowerManager.WakeLock? = null

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "BlockingOverlayService created")
        windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager

        // Create a wake lock to keep the service running
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        wakeLock = powerManager.newWakeLock(
            PowerManager.PARTIAL_WAKE_LOCK,
            "CommitimeTest:BlockingOverlayWakeLock"
        )
        wakeLock?.acquire(10*60*1000L) // 10 minutes timeout

        // Initialize the check runnable
        checkRunnable = Runnable {
            ensureAppBlocked()
            handler.postDelayed(checkRunnable, CHECK_INTERVAL_MS)
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // Create and show a notification immediately to comply with foreground service requirements
        createNotificationChannel()
        val notification = createNotification("Blocking app usage", "Limiting your app time")
        startForeground(NOTIFICATION_ID, notification)

        if (intent != null) {
            blockedAppName = intent.getStringExtra(EXTRA_APP_NAME)
            blockedAppPackage = intent.getStringExtra(EXTRA_PACKAGE_NAME)
            timeLimit = intent.getIntExtra(EXTRA_LIMIT_MINUTES, 0)

            Log.d(TAG, "Starting to block $blockedAppName ($blockedAppPackage) - limit: $timeLimit min")

            createOverlay()

            // Start a periodic check to ensure the app stays blocked
            handler.post(object : Runnable {
                override fun run() {
                    ensureAppBlocked()
                    handler.postDelayed(this, 1000) // Check every second
                }
            })
        }

        return START_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "BlockingOverlayService destroyed")

        // Clean up
        if (overlayView != null) {
            try {
                windowManager.removeView(overlayView)
                overlayView = null
            } catch (e: Exception) {
                Log.e(TAG, "Error removing overlay during onDestroy: ${e.message}")
            }
        }

        // Release wake lock
        wakeLock?.let {
            if (it.isHeld) {
                it.release()
            }
        }

        // Stop checking
        handler.removeCallbacks(checkRunnable)
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "App Blocking",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Used when blocking limited apps"
                setShowBadge(true)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(title: String, content: String): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(title)
            .setContentText(content)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .build()
    }

    private fun createOverlay() {
        // Inflate the overlay layout
        val inflater = getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        overlayView = inflater.inflate(R.layout.activity_blocking, null)

        // Configure the window parameters
        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_SYSTEM_ALERT,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
            WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
            PixelFormat.TRANSLUCENT
        )

        params.gravity = Gravity.CENTER

        // Set title and app name
        overlayView?.findViewById<TextView>(R.id.titleText)?.text = "TIME LIMIT REACHED"
        overlayView?.findViewById<TextView>(R.id.appNameText)?.text = blockedAppName

        // Set text for the blocking message
        overlayView?.findViewById<TextView>(R.id.messageText)?.text =
            "You've reached your $timeLimit minute limit for today"

        // Apply animation to the content
        val animation = android.view.animation.AnimationUtils.loadAnimation(this, R.anim.slide_up)
        overlayView?.startAnimation(animation)

        // Apply pulse animation to the icon
        val imageView = overlayView?.findViewById<ImageView>(R.id.imageView)
        val pulseAnimation = android.view.animation.AnimationUtils.loadAnimation(this, R.anim.pulse)
        imageView?.startAnimation(pulseAnimation)

        // Set up the dismiss button action
        overlayView?.findViewById<Button>(R.id.dismissButton)?.setOnClickListener {
            Log.d(TAG, "Dismiss button clicked, stopping overlay")

            // Force close the app
            forceCloseApp(blockedAppPackage)

            // Explicitly remove the overlay view first
            try {
                if (overlayView != null) {
                    windowManager.removeView(overlayView)
                    overlayView = null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error removing overlay view: ${e.message}")
            }

            // Notify the monitor service that this app was dismissed
            val packageName = blockedAppPackage
            if (packageName != null) {
                try {
                    // Create an intent to tell the monitor service to update its state
                    val intent = Intent(this, AppUsageMonitorService::class.java)
                    intent.action = AppUsageMonitorService.ACTION_APP_UNBLOCKED
                    intent.putExtra("package_name", packageName)

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        startForegroundService(intent)
                    } else {
                        startService(intent)
                    }
                    Log.d(TAG, "Sent unblock notification for $packageName")
                } catch (e: Exception) {
                    Log.e(TAG, "Error notifying monitor service: ${e.message}")
                }
            }

            // Go to home screen
            val homeIntent = Intent(Intent.ACTION_MAIN)
            homeIntent.addCategory(Intent.CATEGORY_HOME)
            homeIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            startActivity(homeIntent)

            // Stop the service
            stopSelf()
        }

        // Set up the "More Time" button action
        overlayView?.findViewById<Button>(R.id.moreTimeButton)?.setOnClickListener {
            Log.d(TAG, "More Time button clicked, navigating to ChatAiScreen")

            // Force close the app
            forceCloseApp(blockedAppPackage)

            // Explicitly remove the overlay view first
            try {
                if (overlayView != null) {
                    windowManager.removeView(overlayView)
                    overlayView = null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error removing overlay view: ${e.message}")
            }

            // Notify the monitor service that this app was dismissed
            val packageName = blockedAppPackage
            if (packageName != null) {
                try {
                    // Create an intent to tell the monitor service to update its state
                    val intent = Intent(this, AppUsageMonitorService::class.java)
                    intent.action = AppUsageMonitorService.ACTION_APP_UNBLOCKED
                    intent.putExtra("package_name", packageName)

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        startForegroundService(intent)
                    } else {
                        startService(intent)
                    }
                    Log.d(TAG, "Sent unblock notification for $packageName")
                } catch (e: Exception) {
                    Log.e(TAG, "Error notifying monitor service: ${e.message}")
                }
            }

            // Launch the Flutter activity with the ChatAiScreen route and pass app info
            val chatIntent = Intent(this, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or
                        Intent.FLAG_ACTIVITY_CLEAR_TOP or
                        Intent.FLAG_ACTIVITY_CLEAR_TASK or
                        Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED
                putExtra("route", "/chat_ai")

                // Pass app name and usage time as extras
                putExtra("app_name", blockedAppName)
                putExtra("usage_time", "$timeLimit minutes")

                Log.d(TAG, "Passing app info to ChatAiScreen: ${blockedAppName}, ${timeLimit} minutes")
            }

            // Make sure the app is brought to the foreground
            startActivity(chatIntent)

            // Stop the service
            stopSelf()
        }

        // Add the view to the window manager
        windowManager.addView(overlayView, params)
        Log.d(TAG, "Overlay added to window manager")
    }

    private fun ensureAppBlocked() {
        blockedAppPackage?.let { packageName ->
            if (isAppInForeground(packageName)) {
                Log.d(TAG, "Detected blocked app in foreground, applying countermeasures (attempt #${++attemptCount})")

                // Force close it again
                forceCloseApp(packageName)

                // Ensure our overlay is visible and properly on top
                if (overlayView != null) {
                    try {
                        // Bring our view to front by touching its z-order
                        windowManager.removeView(overlayView)
                        val type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                        } else {
                            @Suppress("DEPRECATION")
                            WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
                        }
                        val params = WindowManager.LayoutParams(
                            WindowManager.LayoutParams.MATCH_PARENT,
                            WindowManager.LayoutParams.MATCH_PARENT,
                            type,
                            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                                    WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON,
                            PixelFormat.TRANSLUCENT
                        )
                        params.gravity = Gravity.CENTER
                        windowManager.addView(overlayView, params)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error re-attaching overlay: ${e.message}")
                    }
                }

                // Go home as a last resort
                goToHomeScreen()
            }
        }
    }

    private fun isAppInForeground(packageName: String): Boolean {
        val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        // For API 21+
        val appProcesses = activityManager.runningAppProcesses ?: return false

        for (appProcess in appProcesses) {
            if (appProcess.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND &&
                appProcess.processName == packageName) {
                return true
            }
        }

        return false
    }

    private fun forceCloseApp(packageName: String?) {
        packageName?.let {
            try {
                // Try to kill the app's process
                val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
                activityManager.killBackgroundProcesses(packageName)

                // Additional attempt to force stop via shell command on rooted devices
                // This may not work on all devices
                try {
                    Runtime.getRuntime().exec("am force-stop $packageName")
                } catch (e: Exception) {
                    Log.e(TAG, "Error force stopping app via shell: ${e.message}")
                }

                Log.d(TAG, "Forced close attempt for $packageName")
            } catch (e: Exception) {
                Log.e(TAG, "Error killing app process: ${e.message}")
            }
        }
    }

    private fun goToHomeScreen() {
        try {
            val homeIntent = Intent(Intent.ACTION_MAIN).apply {
                addCategory(Intent.CATEGORY_HOME)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            startActivity(homeIntent)
        } catch (e: Exception) {
            Log.e(TAG, "Error going to home screen: ${e.message}")
        }
    }
}