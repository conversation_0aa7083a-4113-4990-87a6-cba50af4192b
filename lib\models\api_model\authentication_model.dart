class AuthenticationModel {
  final String token;
  final String firstName;
  final String lastName;
  final String email;
  final bool isUnder18;
  final String? parentEmail;

  AuthenticationModel(
      {required this.token,
      required this.isUnder18,
      required this.firstName,
      required this.lastName,
      required this.email,
      required this.parentEmail});

  factory AuthenticationModel.fromJson(Map<String, dynamic> json) {
    return AuthenticationModel(
      isUnder18: json['user']['is_under_18'],
      token: json['token'],
      firstName: json['user']['first_name'],
      lastName: json['user']['last_name'],
      email: json['user']['email'],
      parentEmail: json['user']['parent_email'],
    );
  }
}
