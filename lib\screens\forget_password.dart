import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:commitime_test/screens/login.dart';
import 'package:commitime_test/screens/otp_screen.dart';
import 'package:commitime_test/service/forget_password_service.dart';
import 'package:commitime_test/widget/mytextfromfield.dart';

class ForgetPassword extends StatefulWidget {
  const ForgetPassword({super.key});

  @override
  State<ForgetPassword> createState() => _ForgetPasswordState();
}

class _ForgetPasswordState extends State<ForgetPassword> {
  final TextEditingController _textEditingController = TextEditingController();
  bool isloading = false;

  final ForgetPasswordService forgetPasswordService =
      ForgetPasswordService(dio: Dio());

  Future sendEmail() async {
    if (_textEditingController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill out the required Email field'),
        ),
      );
      return;
    }
    setState(() {
      isloading = true;
    });

    final email = await forgetPasswordService.forgetPassword(
        email: _textEditingController.text);
    setState(() {
      isloading = false;
    });
    if (email != null) {
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text("Email send successfully")));
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => OtpScreen(
            email: email,
          ),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text("This Email Not Found"),
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        leading: IconButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const LogIn(),
              ),
            );
          },
          icon: Icon(Icons.arrow_back, color: Colors.black),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 64),
        child: Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Forgot Password?",
                style: const TextStyle(
                  color: Color(0xffBF65EB),
                  fontSize: 24,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                "Recover you password if you have forgot the password!",
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 18,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(height: 36),
              Mytextfromfield(
                controller: _textEditingController,
                lable: "Email",
                hintText: "Ex: <EMAIL>",
                myIcon: Icons.email,
                textInputType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 36),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size(double.infinity, 60),
                  backgroundColor: const Color(0xffBF65EB),
                ),
                onPressed: () {
                  isloading ? null : sendEmail();
                },
                child: isloading
                    ? const CircularProgressIndicator(
                        color: Colors.white,
                      )
                    : Text(
                        "Submit",
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 20,
                        ),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
