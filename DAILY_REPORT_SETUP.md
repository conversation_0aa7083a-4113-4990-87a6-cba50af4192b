# Automatic Daily Report Setup

This document explains how the automatic daily report system works and how to use it.

## Overview

The automatic daily report system sends usage data to your API at the end of each day for all apps that were used for more than 1 minute. The system uses multiple approaches to ensure reliability:

1. **WorkManager Background Tasks** - Scheduled background execution even when app is closed
2. **In-App Timer** - Backup timer when app is running
3. **App Startup Check** - Checks and sends missed reports when app starts

## Files Created

### 1. Model Files
- `lib/models/api_model/daily_report_model.dart` - Data models for API response
  - `DailyReportModel` - Main response model
  - `DailyReportData` - Data section of response
  - `AppUsageDetail` - Individual app usage details

### 2. Service Files
- `lib/service/daily_report_service.dart` - Core API service for sending reports
- `lib/service/automatic_daily_report_service.dart` - Automatic scheduling and management

### 3. Widget Files
- `lib/widget/daily_report_widget.dart` - UI component for manual testing

### 4. Updated Files
- `lib/utils/shared_prefs.dart` - Added methods for storing last report date
- `lib/main.dart` - Integrated automatic service initialization
- `pubspec.yaml` - Added workmanager and cron dependencies
- `android/app/src/main/AndroidManifest.xml` - Added WorkManager permissions

## How It Works

### Automatic Scheduling
1. **Initialization**: Service initializes when app starts
2. **Daily Timer**: Calculates time until midnight and schedules report
3. **Background Task**: Uses WorkManager to run even when app is closed
4. **Periodic Backup**: Runs every 24 hours as additional safety

### Data Collection
1. Gets today's app usage data using `AppUsageService.getUsageStats(1)`
2. Filters apps with usage > 1 minute
3. Formats data according to API requirements
4. Sends to API endpoint with authentication

### Error Handling
- Checks for authentication token before sending
- Handles network errors gracefully
- Logs all operations for debugging
- Prevents duplicate sends by tracking last report date

## Usage Examples

### Basic Integration (Already Done)
The service is automatically initialized in `main.dart`:

```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await AutomaticDailyReportService.initialize();
  runApp(MyApp());
}
```

### Manual Testing
Use the `DailyReportWidget` in your UI:

```dart
// Add to any screen for testing
DailyReportWidget()
```

### Manual Report Sending
```dart
// Send report immediately
final success = await AutomaticDailyReportService.sendReportNow();

// Check if report was sent today
final wasSent = await AutomaticDailyReportService.wasReportSentToday();
```

### Service Management
```dart
// Reschedule (called automatically on app start)
await AutomaticDailyReportService.reschedule();

// Stop the service
await AutomaticDailyReportService.stop();
```

## API Integration

The service sends data to your existing API endpoint:
- **URL**: `https://8ec4-197-35-43-30.ngrok-free.app/api/daily-report`
- **Method**: POST
- **Headers**: Authorization Bearer token, Content-Type application/json

### Request Format
```json
{
  "screen_time_minutes": "300",
  "app_usage_details": "[{\"app\":\"youtube\",\"minutes\":200},{\"app\":\"tiktok\",\"minutes\":100}]",
  "report_date": "2025-06-19"
}
```

### Expected Response
```json
{
  "message": "Daily report sent and stored successfully.",
  "data": {
    "user_id": 66,
    "report_date": "2025-06-19",
    "screen_time_minutes": "300",
    "app_usage_details": "[{\"app\":\"youtube\",\"minutes\":200},{\"app\":\"tiktok\",\"minutes\":100}]",
    "updated_at": "2025-06-18T22:02:24.000000Z",
    "created_at": "2025-06-18T22:02:24.000000Z",
    "id": 50
  }
}
```

## Installation Steps

1. **Install Dependencies**:
   ```bash
   flutter pub get
   ```

2. **Update Android Permissions**: Already done in AndroidManifest.xml

3. **Test the System**:
   - Add `DailyReportWidget()` to your dashboard
   - Use "Send Now" button to test immediate sending
   - Use "Check Status" to verify if report was sent today

## Troubleshooting

### Common Issues
1. **No Authentication Token**: User must be logged in
2. **No Usage Data**: App needs usage stats permission
3. **Network Issues**: Check internet connection and API availability

### Debugging
- Check logs with tag "AutoDailyReport"
- Use the test widget to manually trigger reports
- Verify API endpoint is accessible

### Background Execution
- WorkManager handles background execution
- Android may limit background tasks on some devices
- The in-app timer provides backup when app is running

## Next Steps

1. **Test the Implementation**: Use the test widget to verify everything works
2. **Monitor Logs**: Check for any errors in the automatic scheduling
3. **Customize Timing**: Modify the scheduling if needed (currently set to midnight)
4. **Add Notifications**: Consider adding user notifications when reports are sent

The system is now fully integrated and will automatically send daily reports at the end of each day!
