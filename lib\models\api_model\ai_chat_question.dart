class AiChatQuestion {
  final String question;

  AiChatQuestion({required this.question});

  factory AiChatQuestion.fromjason(Map<String, dynamic> json) {
    // Extract the question from the JSON response
    // Handle cases where the question field might be null or missing
    final questionText = json['question'] ??
        json['message'] ??
        json['response'] ??
        "Is there something else you'd like to spend some time on today?";

    return AiChatQuestion(
      question: questionText,
    );
  }

  // Convert to JSON for debugging or sending back to server
  Map<String, dynamic> toJson() {
    return {
      'question': question,
    };
  }
}
