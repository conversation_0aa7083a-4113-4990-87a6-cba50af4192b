import 'package:flutter/material.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import 'package:commitime_test/screens/account.dart';
import 'package:commitime_test/screens/daschboard.dart';
import 'package:commitime_test/screens/report.dart';

import 'package:commitime_test/screens/statictics.dart';

class Gnav extends StatefulWidget {
  const Gnav({super.key, required this.token});
  final String token;
  @override
  State<Gnav> createState() => _GnavState();
}

class _GnavState extends State<Gnav> {
  int currentPage = 0;
  List<Widget> pageOption = [];
  @override
  void initState() {
    super.initState();
    pageOption = [
      const DashBoard(),
      //const SelectTime(),
      const Statictics(),
      Report(token: widget.token), // <-- مرر التوكن هنا
      const AccountPage(),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: GestureDetector(
        child: Container(
          height: 70,
          width: 70,
          decoration: const BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                  colors: [Color(0xfffdcec3), Color(0xffe2a1ed)])),
          child: const Center(
            child: Icon(
              Icons.add,
              size: 25,
              color: Colors.white,
            ),
          ),
        ),
      ),
      bottomNavigationBar: Container(
        decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(15), topRight: Radius.circular(15))),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
          child: GNav(
              selectedIndex: currentPage,
              backgroundColor: Colors.white,
              color: const Color(0xffe2a1ed),
              activeColor: const Color(0xffe2a1ed),
              tabBorderRadius: 10,
              gap: 8,
              padding: const EdgeInsets.all(16),
              onTabChange: (value) {
                setState(() {
                  currentPage = value;
                });
              },
              tabs: const [
                GButton(
                  icon: Icons.dashboard_sharp,
                  text: "Dashboard",
                ),
                GButton(
                  icon: Icons.show_chart_rounded,
                  text: "Statistics",
                ),
                GButton(
                  icon: Icons.bar_chart_rounded,
                  text: "Report",
                ),
                GButton(
                  icon: Icons.person,
                  text: "Profile",
                )
              ]),
        ),
      ),
      body: pageOption.elementAt(currentPage),
    );
  }
}
