import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:commitime_test/cubits/get_user_data/get_user_data_cubit.dart';
import 'package:commitime_test/screens/customengav.dart';

import 'package:commitime_test/service/authentication_service.dart';
import 'package:commitime_test/utils/shared_prefs.dart';
import 'package:commitime_test/widget/my_password_text_field.dart';
import 'package:commitime_test/widget/mytextfromfield.dart';

class Register extends StatefulWidget {
  const Register({super.key});

  @override
  State<Register> createState() => _RegisterState();
}

class _RegisterState extends State<Register> {
  GlobalKey<FormState> formstate = GlobalKey<FormState>();
  bool password = true;
  bool isUnder18 = false;
  final TextEditingController name = TextEditingController();
  final TextEditingController lname = TextEditingController();
  final TextEditingController email = TextEditingController();
  final TextEditingController password1 = TextEditingController();
  final AuthenticationService authenticationService =
      AuthenticationService(dio: Dio());

  Future registerAdult() async {
    try {
      final result = await authenticationService.registerAdult(
          firstName: name.text,
          lastName: lname.text,
          email: email.text,
          isUnder18: isUnder18,
          password: password1.text);
      final token = result.token;

      log("Successfully registered ");

      // Save user data to SharedPrefs
      await SharedPrefs.setLoggedIn(true);
      await SharedPrefs.setUserToken(token);
      await SharedPrefs.setUserName(name.text);
      await SharedPrefs.setUserEmail(email.text);

      // Check if widget is still mounted before using context
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("Successfully registered"),
          backgroundColor: Colors.green,
        ),
      );

      // Update the user data in the cubit
      context
          .read<GetUserDataCubit>()
          .getUserData(name: name.text, email: email.text);

      // Navigate to the main screen
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) => CustomeNagv(
                  token: token,
                )),
      );
    } catch (e) {
      log("Failed to register: ${e.toString()}");

      // Check if widget is still mounted before using context
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              Text("Email already exists, Or the Password Less than 9 Number"),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 100),
        child: ListView(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Welcome!",
                  style: const TextStyle(
                    color: Color(0xffBF65EB),
                    fontSize: 32,
                    fontFamily: "Aclonica",
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 24),
                const Text(
                  "Create an account to access all the features of CommiTime!",
                  style: TextStyle(color: Colors.black, fontSize: 20),
                ),
                const SizedBox(height: 32),
                Form(
                  key: formstate,
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Mytextfromfield(
                              controller: name,
                              lable: "First Name",
                              hintText: "Enter First Name",
                              myIcon: Icons.person_outline,
                              textInputType: TextInputType.name,
                              textInputAction: TextInputAction.next,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Mytextfromfield(
                              textInputAction: TextInputAction.next,
                              textInputType: TextInputType.name,
                              controller: lname,
                              lable: "Last Name",
                              hintText: "Enter Last Name",
                              myIcon: Icons.person_outline,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 32),
                      Mytextfromfield(
                        textInputType: TextInputType.emailAddress,
                        textInputAction: TextInputAction.next,
                        controller: email,
                        lable: "Email",
                        hintText: "Ex: <EMAIL>",
                        myIcon: Icons.email,
                      ),
                      const SizedBox(height: 32),
                      MyPasswordTextField(
                        password: password,
                        textInputAction: TextInputAction.done,
                        textInputType: TextInputType.visiblePassword,
                        controller: password1,
                        lable: "Password",
                        hintText: "******",
                        myIcon: Icons.lock,
                      ),
                      const SizedBox(height: 32),
                      Container(
                        height: 45,
                        width: double.infinity,
                        decoration: BoxDecoration(
                            color: const Color(0xffBF65EB),
                            borderRadius: BorderRadius.circular(15)),
                        child: TextButton(
                            onPressed: () {
                              if (formstate.currentState!.validate()) {
                                registerAdult();
                              }
                            },
                            child: const Text(
                              "Register",
                              style:
                                  TextStyle(color: Colors.white, fontSize: 18),
                            )),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
