import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:commitime_test/cubits/get_user_data/get_user_data_cubit.dart';
import 'package:commitime_test/screens/customengav.dart';

//import 'package:gradient_borders/input_borders/gradient_outline_input_border.dart';
//import 'package:gradient_icon/gradient_icon.dart';
//import 'package:simple_gradient_text/simple_gradient_text.dart';
import 'package:commitime_test/screens/login.dart';

import 'package:commitime_test/service/authentication_service.dart';
import 'package:commitime_test/utils/shared_prefs.dart';
import 'package:commitime_test/widget/my_password_text_field.dart';
import 'package:commitime_test/widget/mytextfromfield.dart';

class Under18 extends StatefulWidget {
  const Under18({super.key});

  @override
  State<Under18> createState() => _Under18State();
}

class _Under18State extends State<Under18> {
  GlobalKey<FormState> formstate = GlobalKey<FormState>();
  final TextEditingController firstName = TextEditingController();
  final TextEditingController lastName = TextEditingController();
  final TextEditingController email = TextEditingController();
  final TextEditingController password1 = TextEditingController();
  final TextEditingController parentEmail = TextEditingController();
  bool password = true;
  bool isUnder18 = true;
  final AuthenticationService authenticationService =
      AuthenticationService(dio: Dio());
  Future register() async {
    try {
      final result = await authenticationService.registerAdult(
          firstName: firstName.text,
          lastName: lastName.text,
          email: email.text,
          password: password1.text,
          isUnder18: isUnder18,
          parentEmail: parentEmail.text);

      log("Successfully registered ");

      // Save user data to SharedPrefs
      await SharedPrefs.setLoggedIn(true);
      await SharedPrefs.setUserToken(result.token);
      await SharedPrefs.setUserName(firstName.text);
      await SharedPrefs.setUserEmail(email.text);

      // Check if widget is still mounted before using context
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("Successfully registered"),
          backgroundColor: Colors.green,
        ),
      );

      // Update the user data in the cubit
      context
          .read<GetUserDataCubit>()
          .getUserData(name: firstName.text, email: email.text);

      final token = result.token;

      // Navigate to the main screen
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => CustomeNagv(token: token)),
      );
    } catch (e) {
      log("Failed to register: ${e.toString()}");

      // Check if widget is still mounted before using context
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              "Enter Correct Email or Password , or Check Your Internet Connection"),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: null,
      appBar: null,
      extendBodyBehindAppBar: true,
      bottomNavigationBar: null,
      bottomSheet: null,
      extendBody: true,
      body: ListView(
          shrinkWrap: true,
          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
          physics: BouncingScrollPhysics(),
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 100,
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Teenager!",
                      style: const TextStyle(
                          color: Color(0xffBF65EB),
                          fontSize: 32,
                          fontFamily: "Aclonica",
                          fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    const Text(
                      "You are under the legal age. Please enter your parent's email to ensure your safety.",
                      style: TextStyle(color: Colors.black, fontSize: 18),
                    ),
                    const SizedBox(
                      height: 32,
                    ),
                    Form(
                        key: formstate,
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                SizedBox(
                                  height: 70,
                                  width: 175,
                                  child: Mytextfromfield(
                                    controller: firstName,
                                    lable: "First Name",
                                    hintText: "Enter First Name",
                                    myIcon: Icons.person_outline,
                                    textInputType: TextInputType.name,
                                    textInputAction: TextInputAction.send,
                                  ),
                                ),
                                SizedBox(
                                  height: 70,
                                  width: 175,
                                  child: Mytextfromfield(
                                    controller: lastName,
                                    lable: "Last Name",
                                    hintText: "Enter Last Name",
                                    myIcon: Icons.person_outline,
                                    textInputType: TextInputType.name,
                                    textInputAction: TextInputAction.send,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 32,
                            ),
                            Mytextfromfield(
                              controller: email,
                              lable: " Your Email",
                              hintText: "Ex: <EMAIL>",
                              myIcon: Icons.email_outlined,
                              textInputType: TextInputType.emailAddress,
                              textInputAction: TextInputAction.send,
                            ),
                            const SizedBox(
                              height: 32,
                            ),
                            Mytextfromfield(
                              controller: parentEmail,
                              lable: " Parent Email",
                              hintText: "Ex: <EMAIL>",
                              myIcon: Icons.email_outlined,
                              textInputType: TextInputType.emailAddress,
                              textInputAction: TextInputAction.send,
                            ),
                            const SizedBox(
                              height: 32,
                            ),
                            MyPasswordTextField(
                              controller: password1,
                              lable: "Password",
                              hintText: "******",
                              myIcon: Icons.lock,
                              password: true,
                              textInputAction: TextInputAction.send,
                              textInputType: TextInputType.visiblePassword,
                            ),
                            const SizedBox(
                              height: 32,
                            ),
                            Container(
                              height: 45,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                  color: const Color(0xffBF65EB),
                                  borderRadius: BorderRadius.circular(15)),
                              child: TextButton(
                                  onPressed: () {
                                    if (formstate.currentState!.validate()) {
                                      register();
                                    }
                                  },
                                  child: const Text(
                                    "Register",
                                    style: TextStyle(
                                        color: Colors.white, fontSize: 18),
                                  )),
                            ),
                            const SizedBox(
                              height: 32,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Text(
                                  "Already Have An Account? ",
                                  style: TextStyle(
                                      color: Colors.black, fontSize: 16),
                                ),
                                GestureDetector(
                                  onTap: () {
                                    Navigator.push(context, MaterialPageRoute(
                                      builder: (context) {
                                        return const LogIn();
                                      },
                                    ));
                                  },
                                  child: Text(
                                    "LogIn",
                                    style: const TextStyle(
                                        color: Color(0xffBF65EB),
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                        fontFamily: "Acionica"),
                                  ),
                                )
                              ],
                            )
                          ],
                        ))
                  ],
                ),
              ),
            ),
          ]),
    );
  }
}
