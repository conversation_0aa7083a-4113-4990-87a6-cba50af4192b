import 'package:commitime_test/models/individual_bar.dart';

class BarData {
  final double week1;
  final double week2;

  final double week3;

  final double week4;
  final double week5;
  final double week6;
  final double week7;
  final double week8;
  BarData(
      {required this.week1,
      required this.week2,
      required this.week3,
      required this.week4,
      required this.week5,
      required this.week6,
      required this.week7,
      required this.week8});

  List<IndividualBar> barData = [];
  void initializeBardata() {
    barData = [
      IndividualBar(x: 1, y: week1),
      IndividualBar(x: 2, y: week2),
      IndividualBar(x: 3, y: week3),
      IndividualBar(x: 4, y: week4),
      IndividualBar(x: 5, y: week5),
      IndividualBar(x: 6, y: week6),
      IndividualBar(x: 7, y: week7),
      IndividualBar(x: 8, y: week8),
    ];
  }
}
