import 'package:flutter/material.dart';
import 'dart:convert';

// ignore: must_be_immutable
class AppsListTile extends StatelessWidget {
  final String pathImage;
  final String title;
  final double slidervalue;
  final String time;
  final Color? color;
  final String? percentageText;
  final String? limitText;
  final String? iconBase64;
  Function()? onTap;

  AppsListTile({
    super.key,
    required this.pathImage,
    required this.title,
    required this.slidervalue,
    required this.time,
    this.onTap,
    this.color,
    this.percentageText,
    this.limitText,
    this.iconBase64,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 16),
      child: Container(
        height: 130,
        width: 345,
        decoration: BoxDecoration(
            color: color, borderRadius: BorderRadius.circular(26)),
        child: GestureDetector(
          onTap: onTap,
          child: ListTile(
            shape: CircleBorder(
                side: BorderSide(
              color: Color(0xffE9ECF1),
            )),
            isThreeLine: true,
            contentPadding: const EdgeInsets.all(16),
            leading: SizedBox(height: 40, width: 40, child: _buildAppIcon()),
            title: Text(title),
            subtitle: SliderTheme(
              data: SliderTheme.of(context).copyWith(
                thumbShape: const RoundSliderThumbShape(
                    enabledThumbRadius: 0.0), // Remove thumb
                thumbColor: Colors.transparent, // Make thumb invisible
              ),
              child: Slider(
                thumbColor: Colors.white,
                allowedInteraction: SliderInteraction.tapAndSlide,
                activeColor: Colors.purpleAccent,
                value: slidervalue,
                onChanged: (slidervalue) {},
              ),
            ),
            tileColor: Colors.black,
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  time,
                  style: TextStyle(fontSize: 15, fontWeight: FontWeight.w500),
                ),
                if (percentageText != null) ...[
                  Text(
                    "$percentageText% of total",
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
                if (limitText != null) ...[
                  Text(
                    limitText ?? '',
                    style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xffBF65EB),
                        fontWeight: FontWeight.bold),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppIcon() {
    if (iconBase64 != null && iconBase64!.isNotEmpty) {
      try {
        // Clean the base64 string by removing any whitespace or newlines
        final cleanBase64 = iconBase64!.replaceAll(RegExp(r'\s+'), '');

        // Add padding if needed
        final padding = cleanBase64.length % 4;
        final paddedBase64 =
            padding > 0 ? cleanBase64 + '=' * (4 - padding) : cleanBase64;

        return Image.memory(
          base64Decode(paddedBase64),
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            print('Error loading app icon: $error');
            return Image.asset(pathImage);
          },
        );
      } catch (e) {
        print('Error decoding base64 icon: $e');
        print('Base64 string length: ${iconBase64!.length}');
        return Image.asset(pathImage);
      }
    }
    return Image.asset(pathImage);
  }
}
