import 'dart:developer';
import 'package:dio/dio.dart';

final dio = Dio(BaseOptions(
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  },
  followRedirects: false,
  validateStatus: (status) => status != null && status < 500,
));

class ForgetPasswordService {
  final Dio dio;
  final String baseUrl = "https://1984-197-35-7-0.ngrok-free.app/api/";
  final String forgetEndPoint = "forgot-password";
  final String resetPassword = "reset-password";
  final String verifyOtp = "verify-reset-code";

  ForgetPasswordService({required this.dio});

  Future forgetPassword({required String email}) async {
    try {
      final Response response =
          await dio.post("$baseUrl$forgetEndPoint", data: {
        'email': email,
      });
      if (response.statusCode == 200) {
        log("Data sent successfully");
        return email;
        //return response.data;
      } else {
        throw Exception("Failed to send data, ${response.statusCode}");
      }
    } on DioException catch (e) {
      final data = e.response?.data;
      String errMessage = "Oops, there was an error. Try again later.";

      if (data is Map<String, dynamic> && data.containsKey('message')) {
        errMessage = data['message'];
      } else if (data is String) {
        errMessage = data;
      }
      throw Exception(errMessage);
    }
  }

  Future<String> getOtp({required String email, required String otp}) async {
    try {
      final response = await dio.post("$baseUrl$verifyOtp", data: {
        'email': email,
        'code': otp,
      });
      log("OTP Verification Response: ${response.data}");

      if (response.statusCode == 200) {
        log("OTP Verified successfully.");
        return otp;
      } else {
        throw Exception("OTP Verification failed: ${response.data}");
      }
    } on DioException catch (e) {
      log("OTP Error: ${e.response?.data}");
      log(e.toString());
      throw Exception(e.response?.data?['message'] ?? "${e.toString()}");
    }
  }

  Future<String> resetPasswordFun({
    required String email,
    required String otp,
    required String password,
  }) async {
    try {
      final Response response = await dio.post("$baseUrl$resetPassword", data: {
        'email': email,
        'code': otp,
        'password': password,
        'password_confirmation': password, // Add confirmation field
      });

      if (response.statusCode == 200) {
        log("Password Changed Successfully");
        return password;
      } else {
        throw Exception("Password reset failed: ${response.data}");
      }
    } on DioException catch (e) {
      final String errMessage = e.response?.data?['error'] ??
          e.response?.data?['message'] ??
          "Oops, there was an error. Try again later.";
      log(e.toString());
      throw Exception(errMessage);
    }
  }
}
